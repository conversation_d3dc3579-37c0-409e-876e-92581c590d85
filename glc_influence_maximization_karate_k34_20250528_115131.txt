GLC算法 + IC模型 影响力最大化结果
============================================================
网络信息:
  网络名称: karate
  节点数: 34
  边数: 78
  平均度数: 4.59
  网络密度: 0.1390

GLC算法参数:
  λ参数: 0.5

影响力最大化参数:
  种子节点数 k: 34
  传播概率 p: 0.05
  模拟次数: 10000

影响力最大化结果:
  种子集: [0, 33, 2, 32, 1, 31, 8, 13, 3, 19, 30, 23, 27, 7, 28, 29, 5, 6, 4, 10, 9, 12, 17, 21, 14, 15, 18, 20, 22, 25, 24, 26, 11, 16]
  总影响力: 34.0000
  影响力比例: 100.00%

种子节点详细信息:
----------------------------------------
排名   节点     GLC值        
----------------------------------------
1    <USER>      <GROUP>.0000   
2    33     2892.0000   
3    2      1314.0000   
4    32     1268.7500   
5    1      1131.5000   
6    31     1018.5000   
7    8      970.0000    
8    13     970.0000    
9    3      803.0000    
10   19     582.0000    
11   30     580.0000    
12   23     512.1250    
13   27     507.5000    
14   7      488.0000    
15   28     398.7500    
16   29     391.6250    
17   5      366.0000    
18   6      366.0000    
19   4      305.0000    
20   10     305.0000    
21   9      290.0000    
22   12     244.0000    
23   17     244.0000    
24   21     244.0000    
25   14     241.0000    
26   15     241.0000    
27   18     241.0000    
28   20     241.0000    
29   22     241.0000    
30   25     218.2500    
31   24     218.2500    
32   26     210.8750    
33   11     122.0000    
34   16     91.5000     

说明:
- 这是经典的影响力最大化问题
- GLC算法选出前34个最重要节点作为种子集
- IC模型计算种子集的总传播影响力
- 总影响力表示在IC模型下平均能影响多少个节点
