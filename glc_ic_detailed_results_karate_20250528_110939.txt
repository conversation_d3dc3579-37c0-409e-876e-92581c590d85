GLC算法 IC模型 k=1到50 详细评估结果
============================================================
网络类型: karate
网络节点数: 34
网络边数: 78
λ参数: 0.5
传播概率: 0.05
评估范围: k=1 到 k=34
GLC聚类数: 2
全局关键节点数: 2

详细结果:
k值	IC影响力	增长率(%)
----------------------------------------
1	1.93		0.0%
2	3.93		103.6%
3	5.24		171.3%
4	6.41		232.0%
5	7.73		300.6%
6	8.72		351.9%
7	9.50		392.3%
8	10.38		438.0%
9	11.38		489.5%
10	12.13		528.6%
11	13.06		576.5%
12	13.98		624.4%
13	14.84		668.9%
14	15.74		715.4%
15	16.59		759.6%
16	17.53		808.4%
17	18.50		858.3%
18	19.59		915.1%
19	20.52		963.0%
20	21.30		1003.8%
21	22.21		1050.7%
22	23.10		1097.0%
23	23.98		1142.5%
24	24.91		1190.4%
25	25.88		1240.8%
26	26.77		1287.2%
27	27.63		1331.7%
28	28.57		1380.3%
29	29.44		1425.6%
30	30.38		1474.1%
31	31.24		1518.8%
32	32.13		1564.8%
33	33.10		1615.0%
34	34.00		1661.7%

统计信息:
最大影响力: 34.00
最小影响力: 1.93
平均影响力: 18.89
总体增长率: 1661.7%
