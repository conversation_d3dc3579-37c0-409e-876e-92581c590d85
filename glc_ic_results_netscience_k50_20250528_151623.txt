GLC算法 + IC模型 影响力计算结果
============================================================
网络信息:
  网络名称: netscience
  节点数: 379
  边数: 914
  平均度数: 4.82
  网络密度: 0.0128

GLC算法参数:
  λ参数: 0.3

IC模型参数:
  种子节点数 k: 50
  传播概率 p: 0.05
  模拟次数: 10000

结果:
  种子集: [19, 22, 25, 40, 107, 56, 96, 100, 105, 108, 220, 17, 24, 303, 42, 26, 178, 222, 21, 33, 1, 166, 106, 111, 71, 2, 187, 104, 110, 115, 148, 68, 167, 169, 34, 103, 119, 101, 5, 20, 302, 304, 287, 200, 313, 92, 94, 74, 12, 218]
  IC影响力: 67.3957
  影响力比例: 17.78%

GLC节点排名 (前20个):
----------------------------------------
排名   节点     GLC值        
----------------------------------------
1    <USER>     <GROUP>.6133  
2    22     38187.7266  
3    25     32987.1914  
4    40     20618.2422  
5    107    18728.0156  
6    56     15670.7031  
7    96     14343.1250  
8    100    14343.1250  
9    105    14343.1250  
10   108    14343.1250  
11   220    13946.7188  
12   17     11883.1992  
13   24     11342.8477  
14   303    9222.7891   
15   42     9043.1250   
16   26     8360.0918   
17   178    8188.0312   
18   222    8146.6875   
19   21     8113.1191   
20   33     8043.0820   

说明:
- GLC算法选出前50个最重要节点作为种子集
- 使用IC模型计算种子集的实际传播影响力
- IC影响力表示在IC模型下平均能影响多少个节点
