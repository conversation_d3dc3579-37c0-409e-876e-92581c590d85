3. Our approach
Cluster structures are commonly observed in social networks, where 
nodes within a cluster exhibit strong connections among themselves, 
while connections between different clusters are relatively sparse 
(<PERSON> et al., 2013). The theory of social conformity (<PERSON><PERSON><PERSON><PERSON> & <PERSON>, 2004) suggests that individuals in the same cluster tend to align 
their attitudes, beliefs, and behaviors with those of the group. <PERSON> et al. 
(<PERSON> et al., 2015) assert that social influence plays a significant role in 
decision-making, leading to the well-documented phenomenon known 
as the herding effect. This implies that individuals often conform to the 
choices and behaviors of the majority, facilitating the efficient dissemination of information within a group. Therefore, it is reasonable to 
hypothesize that if a node has the capacity to transmit information 
effectively to various clusters within a network, then the influence of 
that node in disseminating information is also substantial. Our main idea 
is that the importance of a node depends not only on the importance of 
its neighboring nodes (the local aspect) but also on its ability to influence key nodes in different groups (the global aspect) within the 
network. To implement this strategy, we propose a novel clustering 
method designed to identify groups within the network and pinpoint 
influential nodes within each group, designating them as global critical 
nodes across the entire network. Then the local influence of a node is 
defined by the number and significance of its neighboring nodes. 
Finally, the total influence of nodes is derived from the above two aspects, thus providing a comprehensive view of their importance within 
the network. The proposed method includes the following three processes, and its flow chart is shown in Fig. 1.
3.1. Group detection and global critical nodes selection
To find the global critical nodes, we proposed a novel clustering 
method for detecting groups. We first select nodes within the network 
that have the potential to form clusters, designating them as initial nodes 
for a group. We then devise rules to gradually incorporate closely related 
nodes from the neighborhood of the initial nodes into the group. 
Through this method, we can systematically derive multiple groups and 
ultimately partition the complex network into several clusters. From 
these groups, we identify influential nodes to serve as global key nodes.
Detail explanations of the proposed clustering process are as follows:
Firstly, The potential of nodes i forming clusters is defined as 
pci = ki⋅
∑
j∈N(i)
kin
j (10) 
where kin
i means the number of links that connecting node j to the node i 
and i’s neighbors. A higher pc value of node i indicates that i has a higher 
degree and the links between its neighbors are closer, suggesting that 
such nodes are more likely to form clustered structures in a network.
Then the strategies for the cluster detection process are designed as 
follows.
Step1: For nodes with the maximum pc value (referred to as pcmax) in 
the network, we select one with the highest degree as the initial node to 
establish cluster C. Subsequently, this node and its neighbors with pc 
values exceeding half of pcmax are add in cluster C.
Step2: The cluster C expand by incorporating neighbouring nodes 
through the following strategy: When examining a neighbouring node i 
of C, we break down its total degree into two components:kin
i , which 
counts the links connecting node i to C, and kout
i , indicating the 
remaining edges extending from node i to the rest of the network. If node 
i meets the condition kin
i ⩾kout
i , it is included in cluster C. Neighbouring 
nodes linked to the subgroup are explored in order of increasing degrees. 
This iterative process is repeated three times according to the three 
degrees of influence rule (Christakis & Fowler, 2013), ultimately 
yielding a tightly interconnected cluster.
Step3: The pc values of nodes in cluster C formed in step 2 are set to 0. 
Then, Step 1 and Step 2 are repeated until the total number of nodes in 
the formed clusters reaches a proportion λ of the total number of nodes 
in the network.
Step4: The spreaders with the highest degree values in each cluster 
are chosen as the global critical nodes.
Our network clustering detection algorithm is efficient and ensures 
the relative dispersion and importance of the selected global critical 
spreaders. Once information initiated from node i successfully reaches 
these global key nodes, it can easily lead to explosive dissemination of 
information within the network.
3.2. Local influence of nodes calculation
The local influence of a node is primarily determined by the number 
and importance of its neighboring nodes. The more numerous and significant the neighboring nodes, the more important the target node 
becomes. Here, we utilize the coreness centrality proposed by Bae et al. 
(Bae & Kim, 2014) to measure the local influence of nodes. The basic 
assumption of coreness centrality is that a spreader with more connections to the neighbors located in the core of the network is more 
powerful, which is defined as: 
LIi = NCCi = ∑
j∈N(i)
ksj (11) 
where ksj represent the k-shell value of node j in the network.
3.3. Calculation of overall influence
The larger the local influence of a node, the more it ensures that 
information initiated from the node is less likely to be confined to a local 
region. Additionally, when nodes are closer to global critical nodes in 
the network, information is more likely to propagate through these 
nodes to different clusters within the network, thereby resulting in the 
widespread dissemination of information. Driven by the fact that the 
accuracy and effectiveness of influential nodes identification can be 
improved by considering the influence of nodes from multiple perspectives. The proposed method GLC takes into account not only the 
local influence of nodes but also their ability to indirectly affect key 
nodes within network clusters. We firstly find the shortest path of all 
other nodes to the obtained global critical nodes. Then GLC combines 
both local and global topological aspects, and is defined as 
GLCi = LIi •
∑
u∈C
LIu
2diu
= ∑
j∈N(i)
ksj •
∑
u∈C
∑
m∈N(u)ksm
2diu
(12) 
where the term ∑
u∈C
LIu
2diu results from the contribution of global critical 
node set C, diu is the shortest path length between node i and node u. A 
larger GLC value indicates that a node has a greater propagation influence in the network.
Algorithm 1. GLC centrality
1 Input:
2 Network G=(V, E) with n=|V|,m=|E|, i = 1
3 Output:
4 In graph G, the GLC is calculated for each node.
5 Begin Algorithm
6 While the total number of nodes in the formed clusters ρ < N*λ
7 for each node a in V do
8 Compute pca using Eq.(10)
9 end for
10 Select node b with maximum pc value (pcmax) as initial node of cluster Ci
11 for node c ∈ Γ(b)
12 if pcc⩾pcmax
2 , assign c in cluster Ci
13 end for
14 for iter:0 → 3 do
15 for d ∈ Γ(Ci) do
16 if kin
d (Ci)⩾kout
d (Ci) then
17 Ci = Ci ∪ {d}
18 end if
19 end for
20 end for
21 for node e ∈ Γ(Ci) do
22 pce = 0
23 end for
24 recalculate the pc value of remaining nodes in G.
25 end while
26 C = {C1,C2,C3,⋯}
27 kv ← compute degree value for each node v in each cluster
28 GC ← find the global critical node with the highest degree values in each 
cluster
29 for each node v in V do
30 compute GLC according to Eq.(12)
31 end for
32 Ranking nodes base on GLC values.
33 End algorithm