GLC算法是一种用于识别复杂网络中有影响力节点的双视角方法，结合了全局和局部视角来评估节点的重要性。以下是对GLC算法流程的详细梳理：


### **GLC算法整体框架**
GLC算法主要包含三个核心步骤：**群组检测与全局关键节点选择**、**局部影响力计算**、**整体影响力整合**。算法流程如下：


### **1. 群组检测与全局关键节点选择**
#### 1.1 节点聚类潜力计算
- **目标**：识别具有形成紧密集群潜力的节点。
- **公式**：  
  \[
  pc_i = k_i \cdot \sum_{j \in N(i)} k_j^{in}
  \]  
  其中，\(pc_i\)为节点\(i\)的聚类潜力，\(k_i\)为节点\(i\)的度，\(k_j^{in}\)为节点\(j\)与节点\(i\)及其邻居的连接数。该公式衡量节点度及其邻居连接的紧密程度，\(pc_i\)越高，节点越易形成集群。

#### 1.2 初始集群构建
- **步骤1**：选择具有最大\(pc_{max}\)的节点作为初始节点，与其邻居中\(pc\)值超过\(pc_{max}/2\)的节点共同组成初始集群\(C\)。

#### 1.3 集群扩展
- **步骤2**：迭代扩展集群\(C\)，规则如下：  
  对于集群\(C\)的邻居节点\(i\)，将其度分为两部分：  
  - \(k_i^{in}\)：节点\(i\)与集群\(C\)的连接数；  
  - \(k_i^{out}\)：节点\(i\)与网络其他部分的连接数。  
  若\(k_i^{in} \geq k_i^{out}\)，则将节点\(i\)加入集群\(C\)。  
  扩展过程重复3次（基于三度影响力规则），形成紧密连接的集群。

#### 1.4 全局关键节点选择
- **步骤3**：将已形成集群的节点的\(pc\)值置为0，重复步骤1-2，直到集群节点总数达到网络节点总数的比例\(\lambda\)。  
- **步骤4**：每个集群中选择度最大的节点作为**全局关键节点**（Global Critical Nodes）。


### **2. 局部影响力计算**
- **目标**：基于节点邻居的重要性评估其局部影响力。
- **公式**：使用邻居核心度（Neighborhood Coreness Centrality, NCC）  
  \[
  LI_i = NCC_i = \sum_{j \in N(i)} ks_j
  \]  
  其中，\(LI_i\)为节点\(i\)的局部影响力，\(ks_j\)为节点\(j\)的k-shell值。该公式表明，节点邻居的k-shell值之和越大，其局部影响力越强。


### **3. 整体影响力整合**
- **目标**：结合局部影响力和全局关键节点的距离，计算节点的整体影响力。
- **公式**：  
  \[
  GLC_i = LI_i \cdot \sum_{u \in C} \frac{LI_u}{2^{d_{iu}}} = \sum_{j \in N(i)} ks_j \cdot \sum_{u \in C} \frac{\sum_{m \in N(u)} ks_m}{2^{d_{iu}}}
  \]  
  其中，\(GLC_i\)为节点\(i\)的整体影响力，\(C\)为全局关键节点集合，\(d_{iu}\)为节点\(i\)到全局关键节点\(u\)的最短路径长度。  
  - 解释：节点的局部影响力\(LI_i\)越大，且到全局关键节点的最短路径越短（\(2^{d_{iu}}\)越小），则整体影响力\(GLC_i\)越大。


### **4. 算法伪代码与复杂度分析**
#### 4.1 伪代码关键步骤
1. **输入**：网络\(G=(V, E)\)，参数\(\lambda\)。  
2. **集群构建循环**：  
   - 计算所有节点的\(pc_i\)（使用公式10）。  
   - 选择\(pc_{max}\)节点作为初始集群，加入\(pc \geq pc_{max}/2\)的邻居。  
   - 迭代3次扩展集群，按\(k_i^{in} \geq k_i^{out}\)规则添加节点。  
3. **全局关键节点选择**：每个集群中选度最大的节点作为全局关键节点。  
4. **GLC计算**：对每个节点，按公式12计算\(GLC_i\)，并排序。

#### 4.2 计算复杂度
- 集群构建：\(O(N)\)（初始节点选择） + \(O(u \cdot N^2)\)（最短路径计算，\(u\)为集群数量）。  
- 整体复杂度：\(O(N^2)\)，适用于中大型网络。


### **5. 关键公式总结**
| 步骤                | 公式编号 | 公式                                                                 | 含义说明                                                                 |
|---------------------|----------|----------------------------------------------------------------------|--------------------------------------------------------------------------|
| 聚类潜力计算        | (10)     | \(pc_i = k_i \cdot \sum_{j \in N(i)} k_j^{in}\)                       | 衡量节点形成集群的潜力，结合节点度和邻居连接紧密性。                     |
| 局部影响力计算      | (11)     | \(LI_i = \sum_{j \in N(i)} ks_j\)                                     | 基于邻居k-shell值之和评估局部影响力。                                     |
| 整体影响力计算      | (12)     | \(GLC_i = LI_i \cdot \sum_{u \in C} \frac{LI_u}{2^{d_{iu}}}\)         | 整合局部影响力和到全局关键节点的距离，距离越近则影响力权重越高。         |


### **6. 算法核心思想**
GLC算法通过以下两点创新提升节点影响力识别的准确性：  
1. **双视角整合**：同时考虑局部邻居重要性（\(LI_i\)）和全局跨集群传播能力（通过全局关键节点的距离）。  
2. **集群与关键节点机制**：通过自定义聚类算法识别紧密集群，并选取集群中的关键节点作为全局传播枢纽，确保信息能高效扩散至整个网络。




Review
GLC: A dual-perspective approach for identifying influential nodes in 
complex networks
Yirun Ruan *
, Sizheng Liu, Jun Tang, Yanming Guo, Tianyuan Yu
Department of Laboratory for Big Data and Decision, National University of Defense Technology, Changsha 410073, China
ARTICLE INFO
Keywords:
Complex network
Node influence
Local influence
Global critical nodes
Cluster algorithm
ABSTRACT
Identifying influential spreaders is crucial for understanding the dynamics of information diffusion within 
complex networks. Several centrality methods have been proposed to address this, but these studies often 
concentrate on only one aspect. To solve this problem, we introduce a dual-perspective approach which considers 
both global and local perspectives for identifying influential nodes in complex networks. From a global 
perspective, if a node has the capability to efficiently transmit information to various clusters within a network, 
then the information originating from that node will quickly spread across a large area. From a local perspective, 
when a node has a greater number of neighbors—especially those that are significant within the network—the 
information emanating from that node is less likely to be confined to a localized region. Based on this under￾standing, we first design a novel clustering method to detect groups in which the connections among nodes are 
denser than those with the rest of the network. The most influential nodes in each group are identified as global 
critical nodes. Subsequently, the local influence of a node is defined by the number and significance of its 
neighboring nodes. Ultimately, nodes are ranked according to their local influence, their proximity to the global 
critical nodes using the shortest paths, and the importance of these global critical nodes. To evaluate the per￾formance of the proposed method, the susceptible-infected-removed (SIR) diffusion model is used. Results of the 
investigation on real networks and realistic synthetic benchmarks show that the proposed method can identify 
nodes with high influence better than other centrality methods.
1. Introduction
With the advent of advancements in network information technol￾ogy, there has been an increasing interest in complex networks research 
across various disciplines. Critical nodes, serving as integral components 
of complex networks, exert a considerable influence on both the struc￾ture and function of the latter. Their role and application in multiple 
arenas have been extensively investigated. For instance, ensuring the 
consistent operation of vital dispatch centers and substations in the 
electricity network can enhance both the stability and efficiency of the 
entire system (Fan et al., 2023). Furthermore, in virus transmission 
networks, the deliberate isolation of super transmission sources can 
dramatically reduce the rate and extent of viral spread (Pastor-Satorras 
& Vespignanj, 2011). Similarly, by manipulating central speech nodes 
within social networks, the promotion or suppression of information 
dissemination could be effectively controlled (Ullah et al., 2021). 
Consequently, an assessment of node significance in these networks 
bears considerable implications.
Numerous pioneering research for identifying influential nodes in 
complex network has been proposed from different aspect. From the 
local level of the network, based on the capacity of nodes to influence 
the behavior of their neighboring nodes, several classical algorithms 
have been proposed, including degree centrality (DC) (Newman, 2003), 
H-index(Lü et al., 2016), ClusterRank (Chen et al., 2013), and Coreness 
(Bae & Kim, 2014). DC is the simplest method to identify the influential 
nodes, but it fails to consider the global structure of networks. Degree 
centrality is simple, but it does not take into account the global structure 
of complex networks, resulting in some seemingly insignificant but 
important nodes being overlooked. H-index centrality considers the in￾fluence of adjacent nodes, while it neglects its own degree and some 
structural information within the network. ClusterRank not only con￾siders the number of nearest neighbors, but also their interactions with 
each other. However, in the presence of social reinforcement, clustering 
may accelerate behavior propagation in online social networks to some 
* Corresponding author.
E-mail addresses: <EMAIL> (Y. Ruan), <EMAIL> (S. Liu), <EMAIL> (J. Tang), <EMAIL> (Y. Guo), 
<EMAIL> (T. Yu). 
Contents lists available at ScienceDirect
Expert Systems With Applications
journal homepage: www.elsevier.com/locate/eswa
https://doi.org/10.1016/j.eswa.2024.126292
Received 20 June 2024; Received in revised form 14 November 2024; Accepted 23 December 2024 
Expert Systems With Applications 268 (2025) 126292
Available online 25 December 2024
0957-4174/© 2025 The Authors. Published by Elsevier Ltd. This is an open access article under the CC BY license (http://creativecommons.org/licenses/by/4.0/).
extent. This may result in the ClusterRank algorithm being less effective 
(Lü et al., 2011). Coreness centrality ranks nodes’ spreading influence 
only by summing all neighbors’k-shell values, ignoring the effect of the 
topological connections among them on the nodes’spreading ability. 
Actually, densely local connections may result in nodes that are not 
actually located in the core of the network being identified have high k￾shell value (Liu et al., 2015; Ruan et al., 2016). Recently, researchers 
pointed out that combining multiple methods can lead to improved 
ranking result. Wang et al. (Wang et al., 2022) put forward a novel 
method for identifying key nodes by aggregating local network struc￾tural information such as ks and degrees of nodes and their surrounding 
nodes. Wen and Jiang (Wen & Jiang, 2019) introduced a fuzzy local 
dimension method, which assesses the influence of nodes by analyzing 
the effect of the central node’s distance on the local dimension using 
fuzzy sets. Zhao and Li (Zhao et al., 2023) evaluate the centrality of a 
node based on its k-shell and structural holes, along with those of its 
neighbors and second-order neighbors, to identify influential nodes in 
complex networks.
At the global level of the network, the global structure generally 
takes into account of the influence of all other nodes on its own nodes 
and has been extensively studied due to its comprehensiveness. The 
most representative approaches are the closeness centrality (CC) and 
betweenness centrality (BC), both of which are given based on paths in 
networks. From the perspective of information propagation, the 
important nodes selected by these methods can help us optimize the 
efficiency of information dissemination under limited resources. How￾ever, due to their high computational complexity, they may not be 
applicable to large-scale networks. Ma et al. (Ma et al., 2016) exploited 
an importance identification method called Gravity centrality to rank 
nodes based on the ks value of nodes and the distance between all pairs 
of nodes, which caused high time complexity. Taking this into consid￾eration, Li et al. (Li et al., 2019) optimized the algorithm by only 
calculating the distance between nodes that are less than the average 
distance of the network, known as local GC. Then, a series of global 
attribute approaches based on the gravity model, including weighted GC 
(WGC) (Liu et al., 2020), the generalized GC (GGC) model (Li et al., 
2021), the combination of H-index and structural holes GC (Ruan et al., 
2022), Laplacian GC (Zhang et al., 2022), effective distance GC (Shang 
et al., 2021), random walk-based GC (Zhao et al., 2022), and 
communicability-based adaptive GC (Xu & Dong, 2024), are proposed 
successively.
Previous ranking methods often failed to consider both global and 
local information of the network simultaneously, leading to the loss of 
valuable information, which frequently results in reduced identification 
accuracy. Therefore, considering both the local and global levels of the 
network, some novel influential nodes identification methods have 
appeared in recent years (Beni & Bouyer, 2020; Berahmand et al., 2018; 
Chiranjeevi et al., 2024; Mehdi Azaouzi;Lotfi Ben Romdhane, 2018; Qu 
et al., 2019; Tidke et al., 2018), which are worthy of further exploration.
To address the problem caused by the lack of information, we pro￾pose a dual-perspective approach that combines both local and global 
perspectives to identify influential nodes in a network. From a global 
perspective, if a node has the capability to efficiently transmit infor￾mation to various clusters within a network, then the information 
originating from the node will quickly spread across a large area. From a 
local perspective, when a node has a greater number of neigh￾bors—especially those that are significant within the network—the in￾formation emanating from the node is less likely to be confined to a 
localized region. Our method strikes a good balance between accuracy 
and complexity.
We summarize the key contributions of this research as follows: (i) 
We introduced a novel clustering technique aimed at distinguishing 
groups within the network and highlighting key nodes within those 
clusters. (ii) We designed an algorithm called (Global-and-Local cen￾trality based on clustering algorithm) GLC considering the influence of 
nodes from multiple perspectives. It incorporates factors such as 
coreness, shortest path between target nodes and key nodes within 
network clusters, and the importance of these critical nodes at a global 
level. (iii) To evaluate the effectiveness of GLC, we conducted a 
comprehensive comparison with several existing benchmark methods. 
Through experimental simulations, our results have demonstrated that 
GLC is capable of effectively identifying important nodes within com￾plex networks.
The structure of this paper is as follows: Section 2 provides an 
overview of the related work. Subsequently, in Section 3, we describe 
the definition and the algorithm flow of our proposed methods. We 
respectively introduced the evaluation metrics criteria and experimental 
dataset in Section 4 and Section 5. The experimental results can be found 
in Section 6. Finally, Section 7 serves as a summary of the entire paper 
and presents the conclusion.
2. Background
In this section, we first define several benchmark centralities. Let any 
network be denoted by G, expressed as G = (V, E), where V and E 
represent nodes and edges, respectively. The method of DC, PRC, H￾index, k-shell, MDD, NCC, KSGC, GRAD and LRAD are defined as 
follows.
2.1. Degree centrality
The degree centrality method (DC) (Newman, 2003) is a straight￾forward and intuitive approach that quantifies the number of a node’s 
neighbors. The degree centrality, ki of node i is denoted as 
ki = ∑N
j=1
aij (1) 
If there is a connection between nodes i and j, aij = 1; otherwise, aij =
0. Degree centrality reflects the node’s direct influence, with a higher 
degree indicating a greater number of links connected to the node.
2.2. PageRank centrality
PageRank Centrality (PRC) (Brin & Page, 1998) estimates the 
importance of a node by considering both the quantity and quality of its 
links. This widely recognized metric is extensively employed for ranking 
websites in Google’s search engine and various commercial applica￾tions, which can be defined as 
PRCi = α
∑
j
Aijxj
/
kout
j + β (2) 
where Aij represents the adjacent matrix, xj is the neighbor of node i and 
kout
j is the out degree of node j. And α and β represent positive constants.
2.3. H-index
The H-index (Lü et al., 2016) was initially used to measure how many 
papers of a scientist have been cited at least h times, where h is the 
number of papers. Lü et al. (Lü et al., 2016) extended this concept to 
networks, suggesting that the H-index of a node represents the number 
of its neighbors, each with a degree not less than h, denoted as 
Hi = ξ
(
kj1, kj2, …, kjs,…, kjki
) (3) 
where, kjs denotes the degree of the s-th neighbor of node i. In the 
equation, the operator H returns the largest integer h, such that node i 
has at least h neighbors with a degree not less than h.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
2
2.4. K-shell decompositon method
Kitsak et al. (Kitsak et al., 2010) introduced the k-shell decomposi￾tion method (KS) to determine the positions of nodes within a network. 
This method involves iterative decomposition of the network into 
different layers or cores. The specific decomposition process is as fol￾lows: It begins by removing all nodes with a degree of 1 from G and 
assigning each such node a ks value of 1. When these degree-1 nodes and 
their connected edges are removed, it may introduce new degree-1 
nodes in the remaining network. The process is repeated recursively, 
removing the degree-1 nodes, until there are no more degree-1 nodes left 
in the network. This recursive process is applied to nodes with degrees of 
2, 3, and so on up to k. Ultimately, nodes in the innermost shells are 
more influential and central to the network structure.
2.5. Mixed degree decomposition method
Zeng et al. (Zeng & Zhang, 2013) made modifications to the k-shell 
method and introduced a mixed degree decomposition (MDD) that takes 
into account information about nodes that have been removed during 
the shell decomposition process. This method considers both the nodes 
that are still in the network (residual nodes) and the nodes that have 
been removed (exhausted nodes), in contrast to the traditional k-shell 
method, which only focuses on residual nodes. In the MDD approach, 
the residual node degree of a node vi is defined as the number of edges 
connected to the nodes that remain in the network, denoted as kr
(vi) . 
The exhausted node degree is the number of edges connected to the 
nodes that have been removed, denoted as ke(vi). The mixed degree of a 
node vi is then calculated as 
km(vi) = kr
(vi) + γ ∗ ke
(vi) (4) 
where γ is a tunable free parameter between 0 and 1.
2.6. Neighborhood coreness centrality
The authors proposed the method under the assumption that a node 
with a greater number of connections to neighbors located in the net￾work’s core is more influential (Bae & Kim, 2014). Building on this 
premise, the authors define the neighborhood coreness, denoted as NCC 
of a node i as shown in Equation below 
NCCi = ∑
j∈N(i)
ksj (5) 
where N(i) is the neighbors set of node i and ksj is the k-shell value of it’s 
neighboring node j.
2.7. KSGC centrality
Yang et al. (Yang & Xiao, 2021) pointed out that the position of a 
node is a crucial attribute within a network, yet many node importance 
assessment algorithms often neglect this aspect. As a result, they 
developed an improved approach, denoted as KSGC, which integrates 
the k-core decomposition method with Newton’s law of universal 
gravitation. This method aims to discern the propagation influence of 
nodes in complex networks, denoted as 
KSGC(vi) = ∑
dij⩽R
cij
kikj
d2
ij
(6) 
where cij = e ksi− ki
ksmax− ksmin , dij represents the distance from node i to node j, R 
represents the network truncation radius, ksmax represents the maximum 
value of ks in the network, while ksmin represents the minimum value of 
ks.
2.8. GRAD and LRAD method
The author extended centrality measurement based on relative 
changes in centrality (Hajarathaiah et al., 2023). They defined the local 
and global centrality measures of a node by observing the impact of any 
relative changes in centrality after the node is removed. When this 
centrality is measured by degree centrality, the global metric of this 
method is called GRAD, and the local metric is called LRAD. Their def￾initions are as follows: 
AD[G] =
∑
v∈Vdv
N (7) 
where dv represents the degree of a node v. The global relative change in 
average degree of a node v in graph G can be determined as: 
GRAD(v) =
⃒
⃒AD[
G
AD
ʹ
v
]
−
[G
AD
]
[G]
⃒
⃒
, v ∈ V (8) 
The local relative change in average degree of a node v in graph G 
can be determined as: 
LRADL(v) = |AD[GNL(v)\v] − AD[GNL (v)]|
AD[GNL(v)] (9) 
where GNL(v)\v is a graph GNL(v) after deleting a vertex v. NL(v) represents 
the induced subgraph composed of neighboring nodes within the L layer 
containing node v, where L is set to half the network diameter.
3. Our approach
Cluster structures are commonly observed in social networks, where 
nodes within a cluster exhibit strong connections among themselves, 
while connections between different clusters are relatively sparse 
(Zhang et al., 2013). The theory of social conformity (Cialdini & Gold￾stein, 2004) suggests that individuals in the same cluster tend to align 
their attitudes, beliefs, and behaviors with those of the group. Xu et al. 
(Xu et al., 2015) assert that social influence plays a significant role in 
decision-making, leading to the well-documented phenomenon known 
as the herding effect. This implies that individuals often conform to the 
choices and behaviors of the majority, facilitating the efficient dissem￾ination of information within a group. Therefore, it is reasonable to 
hypothesize that if a node has the capacity to transmit information 
effectively to various clusters within a network, then the influence of 
that node in disseminating information is also substantial. Our main idea 
is that the importance of a node depends not only on the importance of 
its neighboring nodes (the local aspect) but also on its ability to influ￾ence key nodes in different groups (the global aspect) within the 
network. To implement this strategy, we propose a novel clustering 
method designed to identify groups within the network and pinpoint 
influential nodes within each group, designating them as global critical 
nodes across the entire network. Then the local influence of a node is 
defined by the number and significance of its neighboring nodes. 
Finally, the total influence of nodes is derived from the above two as￾pects, thus providing a comprehensive view of their importance within 
the network. The proposed method includes the following three pro￾cesses, and its flow chart is shown in Fig. 1.
3.1. Group detection and global critical nodes selection
To find the global critical nodes, we proposed a novel clustering 
method for detecting groups. We first select nodes within the network 
that have the potential to form clusters, designating them as initial nodes 
for a group. We then devise rules to gradually incorporate closely related 
nodes from the neighborhood of the initial nodes into the group. 
Through this method, we can systematically derive multiple groups and 
ultimately partition the complex network into several clusters. From 
these groups, we identify influential nodes to serve as global key nodes. 
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
3
Detail explanations of the proposed clustering process are as follows:
Firstly, The potential of nodes i forming clusters is defined as 
pci = ki⋅
∑
j∈N(i)
kin
j (10) 
where kin
i means the number of links that connecting node j to the node i 
and i’s neighbors. A higher pc value of node i indicates that i has a higher 
degree and the links between its neighbors are closer, suggesting that 
such nodes are more likely to form clustered structures in a network.
Then the strategies for the cluster detection process are designed as 
follows.
Step1: For nodes with the maximum pc value (referred to as pcmax) in 
the network, we select one with the highest degree as the initial node to 
establish cluster C. Subsequently, this node and its neighbors with pc 
values exceeding half of pcmax are add in cluster C.
Step2: The cluster C expand by incorporating neighbouring nodes 
through the following strategy: When examining a neighbouring node i 
of C, we break down its total degree into two components:kin
i , which 
counts the links connecting node i to C, and kout
i , indicating the 
remaining edges extending from node i to the rest of the network. If node 
i meets the condition kin
i ⩾kout
i , it is included in cluster C. Neighbouring 
nodes linked to the subgroup are explored in order of increasing degrees. 
This iterative process is repeated three times according to the three 
degrees of influence rule (Christakis & Fowler, 2013), ultimately 
yielding a tightly interconnected cluster.
Step3: The pc values of nodes in cluster C formed in step 2 are set to 0. 
Then, Step 1 and Step 2 are repeated until the total number of nodes in 
the formed clusters reaches a proportion λ of the total number of nodes 
in the network.
Step4: The spreaders with the highest degree values in each cluster 
are chosen as the global critical nodes.
Our network clustering detection algorithm is efficient and ensures 
the relative dispersion and importance of the selected global critical 
spreaders. Once information initiated from node i successfully reaches 
these global key nodes, it can easily lead to explosive dissemination of 
information within the network.
3.2. Local influence of nodes calculation
The local influence of a node is primarily determined by the number 
and importance of its neighboring nodes. The more numerous and sig￾nificant the neighboring nodes, the more important the target node 
becomes. Here, we utilize the coreness centrality proposed by Bae et al. 
(Bae & Kim, 2014) to measure the local influence of nodes. The basic 
assumption of coreness centrality is that a spreader with more connec￾tions to the neighbors located in the core of the network is more 
powerful, which is defined as: 
LIi = NCCi = ∑
j∈N(i)
ksj (11) 
where ksj represent the k-shell value of node j in the network.
3.3. Calculation of overall influence
The larger the local influence of a node, the more it ensures that 
information initiated from the node is less likely to be confined to a local 
region. Additionally, when nodes are closer to global critical nodes in 
the network, information is more likely to propagate through these 
nodes to different clusters within the network, thereby resulting in the 
widespread dissemination of information. Driven by the fact that the 
accuracy and effectiveness of influential nodes identification can be 
improved by considering the influence of nodes from multiple per￾spectives. The proposed method GLC takes into account not only the 
local influence of nodes but also their ability to indirectly affect key 
nodes within network clusters. We firstly find the shortest path of all 
other nodes to the obtained global critical nodes. Then GLC combines 
both local and global topological aspects, and is defined as 
GLCi = LIi •
∑
u∈C
LIu
2diu
= ∑
j∈N(i)
ksj •
∑
u∈C
∑
m∈N(u)ksm
2diu
(12) 
where the term ∑
u∈C
LIu
2diu results from the contribution of global critical 
node set C, diu is the shortest path length between node i and node u. A 
larger GLC value indicates that a node has a greater propagation influ￾ence in the network.
Algorithm 1. GLC centrality
1 Input:
2 Network G=(V, E) with n=|V|,m=|E|, i = 1
3 Output:
4 In graph G, the GLC is calculated for each node.
5 Begin Algorithm
6 While the total number of nodes in the formed clusters ρ < N*λ
7 for each node a in V do
(continued on next page)
Fig. 1. The algorithm framework flow of GLC.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
4
(continued )
8 Compute pca using Eq.(10)
9 end for
10 Select node b with maximum pc value (pcmax) as initial node of cluster Ci
11 for node c ∈ Γ(b)
12 if pcc⩾pcmax
2 , assign c in cluster Ci
13 end for
14 for iter:0 → 3 do
15 for d ∈ Γ(Ci) do
16 if kin
d (Ci)⩾kout
d (Ci) then
17 Ci = Ci ∪ {d}
18 end if
19 end for
20 end for
21 for node e ∈ Γ(Ci) do
22 pce = 0
23 end for
24 recalculate the pc value of remaining nodes in G.
25 end while
26 C = {C1,C2,C3,⋯}
27 kv ← compute degree value for each node v in each cluster
28 GC ← find the global critical node with the highest degree values in each 
cluster
29 for each node v in V do
30 compute GLC according to Eq.(12)
31 end for
32 Ranking nodes base on GLC values.
33 End algorithm
3.4. Computational complexity analysis
In this section, we analyze the computational complexity of the 
proposed method by examining the provided pseudo-code. The clus￾tering process consists of three parts: (i) Forming initial clusters (lines 
7–9): This step involves selecting the node with the maximum pc value 
and its neighboring nodes with pc values exceeding half of pcmax.The 
computational cost of this part is O(N + k) = O(N). (ii) Node classifi￾cation (lines 10–20): When considering the current cluster Ci with nc 
nodes, the complexity of dividing the neighboring nodes of Ci is 
O
(
r⋅nc〈k〉
2
)
, where r is a small constant, and nc < N. (iii) Recalculating 
pc values for the remaining nodes (lines 21–24): This step has a linear 
time complexity of O(N). If the number of clusters formed is denoted as 
u, where u is significantly smaller than N, the computational complexity 
of the entire clustering process is: O(u(N + r⋅nc < k>2 + N)) = O(2 N). In 
line 28, the complexity of computing the degree value in each cluster 
and finding the global critical node with the highest degree values is O 
(N). In lines 29–31, the shortest path between each node and the global 
critical nodes is computed, which has a computational cost of O
(
uN2)
. 
Finally, in line 32, the nodes of the network are ranked. Based on the 
above analysis, the overall computational complexity is O
(
2N + N +
uN2 + Nlog(N)
) = O
(
N2)
.
4. Evaluation metrics criteria
The SIR model (Castellano & Pastor-Satorras, 2010; Newman, 2002) 
is extensively employed by researchers to analyze the spread of infor￾mation and viruses in networks. This model classifies each network node 
into one of three states: Susceptible (S), Infected (I), or Removed (R). At 
the onset of an outbreak, only one node, referred to as node v, is infected, 
while all others remain susceptible. As the model progresses, infected 
nodes attempt to transmit the infection to adjacent susceptible nodes at a 
infection rate β, and convert to the removed state R with a probability of 
μ, thereafter becoming immune to reinfection. The spreading event 
concludes when there are no remaining infected nodes. The final count 
of nodes in the removed state R serves as an indicator of the transmission 
capacity of node v. For simplicity, this analysis sets the recovery rate μ to 
1. It is critical to select an appropriate transmission rate β; rates that are 
too low or too high hinder the normal progression of the epidemic, thus 
skewing the accurate assessment of each node’s transmission efficacy. 
The transmission capacity of a node, designated as Φ(v) = M
1∑M
m=1Φʹ
(v), 
is determined after conducting M iterations of SIR information dissem￾ination experiments, with Φʹ
(v) denoting the number of nodes in state R 
at the end of an experiment where node v was the initial infected source.
This study employs the Kendall tau correlation coefficient (Kendall, 
1945; Knight, 1966) θ to evaluate the accuracy of the proposed algo￾rithm’s node importance rankings relative to alternative metrics. Spe￾cifically, the correlation is assessed between the rankings produced by 
various importance metrics and those based on node propagation in￾fluence as determined by the SIR model. This approach enables a 
quantitative comparison of how well different metrics reflect the actual 
influence of nodes in information dissemination. Its expression is 
θ(R1, R2) = nc − nd ̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅̅ (nt − nu)(nt − nv) √ (13) 
where, R1 and R2 represent two different importance ranking sequences 
of nodes. nc and nd respectively represent the number of concordant 
pairs and discordant pairs in these two permutations. nt = n(n − 1)/2, 
nu = ∑s
i=1ui(ui − 1), nv = ∑t
i=1vi(vi − 1), nu and nv are calculated based 
on R1 and R2 respectively. Using nu as an example for explanation (the 
calculation process for nv can be inferred): Group the identical elements 
in R1 into smaller sets, denoted by s, where ui represents the number of 
elements in the i-th set. The Kendall tau correlation coefficient is utilized 
to compute the results. A higher tau value suggests a greater alignment 
between the node importance rankings derived from the evaluation 
metric and those obtained from the SIR simulation, indicating a more 
precise evaluation.
5. Data
To assess the efficacy of the proposed method for evaluating node 
spreading influence, this study selected six real networks with diverse 
topological characteristics, including 1) Netscience (Newman, 2006), a 
collaborative network among network scientists; 2) Facebook (Blagus 
et al., 2012), representing social connections within Slavo Zitnik’s 
Facebook friend circle; 3) Infectious (Isella et al., 2011), a network 
illustrating population infection dynamics; 4) Yeast (Jeong et al., 2001), 
a protein–protein binding network; 5) Protain (Rual et al., 2005), a 
network depicting interactions between proteins; and 6) CA-GrQc 
(Dorogovtsev et al., 2006), a network published on preprints. The to￾pological characteristics of the six real networks and the three simulated 
networks are summarized in Table 1, including L (average shortest 
distance length), C (average clustering coefficient), βth (SIR epidemic 
threshold(Castellano & Pastor-Satorras, 2010)), ksmax (maximum k-Shell 
value for the entire network), and ksmin (minimum k-shell value for the 
entire network).
6. Result
The SIR model provides a reasonable simulation of the propagation 
process. In this context, we use the number of nodes influenced by the 
SIR model as a benchmark and compare it with the results obtained 
through various metrics. Using the Facebook network as an example, we 
set the information propagation rate to the propagation threshold βth 
and plot the correlation of influence metrics in Facebook, as shown in 
Fig. 2. In Fig. 2(a) to (j), it is evident that the evaluation values of the 
PageRank metric, H-index, degree centrality, KSGC, LRAD, GRAD, and 
K-shell exhibit a relatively weak correlation with the number of nodes 
influenced by the SIR model. In particular, the evaluation values derived 
from the K-shell method show limited differentiation, indicating its 
weaker ability to assess node influence. Conversely, coreness centrality, 
the MDD algorithm, and the GLC algorithm demonstrate a relatively 
strong positive correlation with the number of nodes influenced by the 
SIR model. This implies that, much like the superior coreness centrality 
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
5
and the MDD algorithm, the GLC algorithm provides a reasonably ac￾curate assessment of a node’s propagation influence.
To further evaluate the effectiveness of the algorithm, we assessed 
the rank correlation efficacy of various ranking methodologies across 
both real and synthetic networks, utilizing infection rate β = (βth ± 7 %). 
When βth is less than or equal to 0.07, the spreading rate p was adjusted 
to span from 0.01 to 0.15. As depicted in Fig. 3, the correlations for all 
tested algorithms vary with the infection rate. Notably, our proposed 
method outperforms others, particularly when the value of p exceeds βth. 
This enhancement in performance is attributed to the GLC algorithm’s 
ability to consider both the local influence of nodes and their capacity to 
affect crucial nodes within network clusters. With higher information 
propagation rates, nodes are more likely to influence globally significant 
nodes, facilitating extensive information spread throughout the 
network. Consequently, our metric exhibits increased accuracy under 
these conditions. Additionally, it is observed that the performance of the 
NCC and KSGC algorithms generally surpasses all others, except for the 
GLC algorithm. Conversely, the GRAD and LRAD algorithm consistently 
underperforms compared to the other seven algorithms, with its relative 
deficiency becoming more marked as the propagation rate escalates.
In addition to real datasets, artificial datasets generated using the 
Lancichinetti-Fortunato-Radicchi (LFR) (Lancichinetti et al., 2008) 
model were employed in the experiments. Different network structures 
were created by adjusting parameters. The LFR model parameters were 
set as follows: N = 2000 (number of nodes), minc = 20 (minimum 
community size), maxc = 50 (maximum community size), maxk = 30 
(maximum degree of the network), and mu = 0.1 (mixing parameter). 
The average degree < k > of the network was adjusted to control 
Table 1 
Information of datasets, N,M, <k>, βth , ksmax , ksmin , L, C are number of nodes, edges, average degree, SIR epidemic threshold, maximum k-Shell value, minimum k￾shell value of the network, the average shortest distance between nodes and cluster coefficient respectively.
Network N M <k> βth ksmax ksmin L C
Netscience 379 914 4.8232 0.125 8 1 6.0419 0.7412
Facebook 324 2218 13.69 0.047 18 1 3.054 0.4658
Infectious 410 2765 13.49 0.053 17 1 3.6309 0.4558
Yeast 1458 1948 2.6722 0.140 5 1 6.8124 0.0708
Protain 2783 6726 4.472 0.063 6 1 4.8398 0.0715
CA-GrQc 4158 13,422 6.4560 0.056 43 1 6.0494 0.5569
LFR5 2000 10,034 5.017 0.098 8 1 5.6984 0.3774
LFR10 2000 20,634 10.317 0.072 11 1 4.472 0.4104
LFR15 2000 30,350 15.175 0.058 11 1 3.923 0.4239
Fig. 2. Correlation between the evaluation values of different metrics in Facebook networks and the number of infected nodes in the SIR model.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
6
network density, resulting in the creation of three datasets with values 
of < k>=5, <k>=10, and < k>=15. As shown in Fig. 4, it can be 
observed that in networks with < k>=5 and < k>=10, the proposed 
method can achieve higher ranking accuracy under a wider range of 
information propagation rates, showing a distinct advantage compared 
to other ranking algorithms. It is worth noting that compared to the 
performance of real datasets, the GRAD and LRAD algorithms perform 
better in artificial datasets, especially when the network connections are 
tighter, and the advantages of LRAD are more prominent. In network 
with value of < k>=15, the advantages of the GLC algorithm are not as 
prominent compared to other ranking indicators. This is because in 
highly clustered networks, large-scale clusters are more likely to form. In 
such scenarios, the network groups identified by the GLC algorithm are 
large, but few in number. Consequently, a smaller number of global 
critical nodes are identified, which therefore reduces the precision of the 
algorithm.
To investigate the performance of the algorithm in identifying 
influential propagators, we use the imprecision function proposed by 
Kitsak et al.(Kitsak et al., 2010) for quantification. The definition of the 
imprecision function is: 
η(p) = 1 − M(p)
Meff(p) (14) 
Fig. 3. The networks are (a) Netscience,(b) Facebook, (c) Infectious,(d) Yeast, (e) Protain, (f) CA-GrQc. The color of each point represents the nodes’ spreading 
influence in all six real networks.The dotted lines correspond to the epidemic threshold βth and the infection rate β = (βth ± 7 %), (if βth ≤ 0.07, the infection rate 
changes from 0.01 to 0.15).
Fig. 4. Comparison of ranking accuracy among different method on three LFR datasets: (a) LFR,k = 5; (b) LFR,k = 10; (c) LFR,k = 15.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
7
p is the proportional fraction of the network size N (p ∈ [0, 1]), repre￾senting the top pN nodes obtained by selecting the centrality measure. M 
(p) is the average spreading efficiency of these nodes, which is the 
average value of node spreading ability obtained through SIR simula￾tion. Meff(p) is the average spreading ability of the top pN nodes in SIR 
simulation, represented as the optimal spreading efficiency. Therefore, 
this function quantifies the closeness between the average spreading and 
the optimal spreading of pN nodes with the highest centrality. The closer 
the value of η is to 0, the more centrality can reflect the propagation 
ability of the node.
The imprecision function values of six real network datasets and 
three LFR datasets are shown in Fig. 5. It can be seen that the GLC has 
low imprecision in most networks. In the Netscience, CA-GrQc network, 
the GLC has significant advantages compared to other benchmark al￾gorithms. In situations where the imprecision of other algorithms are 
generally high and fluctuating, the results obtained by the GLC can 
remain stable at a low level. In other networks, although the difference 
with other algorithms is not significant, they are basically kept at the 
lowest level and very stable. Meanwhile, NCC, KSGC, H-index, and MDD 
also perform well, but KS performs poorly. Therefore, our proposed GLC 
has stable performance on both real and synthetic networks, and can 
effectively reflect important nodes with strong propagation power.
To systematically study the impact of parameter λ on the GLC 
method’s performance, we compute the average Kendall’s tau, denoted 
as < θ>, using the formula (Lin et al., 2014): 
< θ >= 1
M
∑
β=βmax
β=βmin
θ(β) (15) 
where M represents the number of spreading rates, β stands for a specific 
Fig. 5. The imprecision function values of six real network datasets and three LFR datasets. (a) Facebook, (b) Netscience, (c) Infectious,(d) Yeast, (e)Protain, (f)CA￾GrQc, (g) LFR,k = 5, (h) LFR,k = 10, (i) LFR,k = 15.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
8
spreading rate, while βmin and βmax signify the smallest and largest 
spreading rates, respectively. θ(β) is the Kendall’s tau derived by 
comparing the ranking list from the SIR process to those produced by the 
GLC method at a given spreading rate of β. The average Kendall’s tau θ 
ranges between [− 1, 1]. Here we assign M = 15, βmin = 0.01 and βmax =
0.15 in Facebook, Infectious, Protain, and CA-GrQc network, βmin = 0.06 
and βmax = 0.19 in Netscience network, βmin = 0.07 and βmax = 0.21 in 
Yeast network. An increase <θ> value indicates that the method can 
more accurately identify the influence of node spreading. In this way, we 
can investigate under which λ the GLC method can achieve the largest 
average Kendall’s tau θ, which indicates the GLC method ranking can 
most accurately identify the node spreading influence. The pertinent 
results are depicted in Fig. 6. The evolution of the optimal λ* in relation 
to β is demarcated by the purple curves within the Figure. For every 
network analyzed, there exists an optimal λ that allows the GLC method 
to reach its maximum θ value. The optimal λ values for the networks are 
as follows: Netscience (1), Facebook (0.95), Infectious (0.4), Yeast (0.7), 
Protein (0.05), and CAGrQc (0.4). In the networks of Facebook, Nets￾cience, Infectious, Yeast, and CAGrQc, we observe that the value of <θ>
generally increases with λ. This trend suggests that, in these networks, 
the cluster detection process of the GLC algorithm can progressively 
identify nodes that are globally influential and have robust information 
propagation capabilities. Conversely, in the Protein network, the trend is 
opposite: < θ> tends to decrease as λ rises. This can be attributed to the 
relative sparseness of the Protein network.
The structure of six real networks is shown in Fig. 7. By observing the 
structural diagram of the networks, it can be noted that only the protein 
network does not exhibit a clear clustering structure. Therefore, in the 
early phases of cluster detection within this network, the GLC algorithm 
effectively pinpoints significant nodes within clusters that have potent 
information dissemination capacities. However, as the process ad￾vances, the key nodes identified in later-found clusters aren’t genuinely 
adept at widespread information propagation. This phenomenon results 
in a declining <θ> value with increasing λ.
7. Conclusion
The precise identification of influential nodes in complex networks 
has emerged as a prominent research topic in network science. This 
paper presents a method called GLC, which not only considers the local 
influence of nodes but also accounts for their ability to indirectly impact 
key nodes within network clusters. By incorporating both direct and 
indirect influence, GLC offers a comprehensive approach to accurately 
assess the significance of nodes in complex networks. The experimental 
results on six real networks and three LFR simulated datasets indicate 
that, compared to other ranking methods such as degree centrality, H 
index, PageRank, k-shell, NCC, MDD, KSGC, GRAD and LRAD method, 
the proposed method demonstrates distinct advantages in identifying 
the spreading ability of network nodes. In most networks, when the 
propagation rate exceeds the propagation threshold, the proposed al￾gorithm can more accurately assess the influence of nodes at different 
proportions.
In this paper, when calculating the distance between nodes and 
globally important nodes in the network, only the shortest paths be￾tween nodes are utilized as effective information. However, in reality, 
other reachable paths between nodes, in addition to the shortest paths, 
are also valid for measuring the interaction effects. Furthermore, the 
process our algorithm uses to identify network clusters is sequential, 
searching one by one. This significantly affects the execution efficiency 
of our algorithm and makes it less efficient in large-scale networks. In 
future work, we aim to enhance both the accuracy and efficiency of the 
algorithm in the following ways. First, we plan to explore parallel pro￾cessing techniques, allowing the algorithm to identify multiple clusters 
simultaneously rather than sequentially. In future work, we aim to 
enhance the accuracy of the algorithm by considering these factors. 
Additionally, we will investigate the integration of machine learning 
methods to identify globally important nodes. These key nodes typically 
exhibit strong local connectivity and occupy central positions within the 
network. By leveraging such techniques, we can better identify the 
shared characteristics of these nodes, leading to significant 
Fig. 6. The impact of parameter λ on the GLC method’s performance in six real network datasets: (a) Netscience, (b) Facebook, (c) Infectious,(d) Yeast, (e) Protain, 
(f) CA-GrQc.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
9
improvements in both the accuracy and efficiency of the algorithm.
Declaration of competing interest
The authors declare that they have no known competing financial 
interests or personal relationships that could have appeared to influence 
the work reported in this paper.
Acknowledgement
This work was supported by the National Natural Science Foundation 
of China, Grant number 72101265.
Data availability
Data will be made available on request.
References
Bae, J., & Kim, S. (2014). Identifying and ranking influential spreaders in complex 
networks by neighborhood coreness. Physica A: Statistical Mechanics and Its 
Applications, 395, 549–559. https://doi.org/10.1016/j.physa.2013.10.047
Beni, H. A., & Bouyer, A. (2020). TI-SC: Top-k influential nodes selection based on 
community detection and scoring criteria in social networks. Journal of Ambient 
Intelligence and Humanized Computing, 11(11), 4889–4908. https://doi.org/10.1007/ 
s12652-020-01760-2
Berahmand, K., Bouyer, A., & Samadi, N. (2018). A new centrality measure based on the 
negative and positive effects of clustering coefficient for identifying influential 
Fig. 7. Structural diagrams of six real networks. (a) Netscience, (b) Facebook, (c) Infectious,(d) Yeast, (e) Protain, (f) CA-GrQc.
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
10
spreaders in complex networks. Chaos, Solitons & Fractals, 110, 41–54. https://doi. 
org/10.1016/j.chaos.2018.03.014
Blagus, N., Subelj, ˇ L., & Bajec, M. (2012). Self-similar scaling of density in complex real￾world networks. Physica A: Statistical Mechanics and Its Applications, 391(8), 
2794–2802. https://doi.org/10.1016/j.physa.2011.12.055
Brin, S., & Page, L. (1998). The anatomy of a large-scale hypertextual Web search engine. 
Computer Networks and ISDN Systems, 30(1–7), 107–117. https://doi.org/10.1016/ 
S0169-7552(98)00110-X
Castellano, C., & Pastor-Satorras, R. (2010). Thresholds for epidemic spreading in 
networks. Physical Review Letters, 105(21), Article 218701. https://doi.org/10.1103/ 
PhysRevLett.105.218701
Chen, D.-B., Gao, H., Lü, L., & Zhou, T. (2013). Identifying influential nodes in large-scale 
directed networks: The role of clustering. PLoS ONE, 8(10), Article e77455. https:// 
doi.org/10.1371/journal.pone.0077455
Chiranjeevi, M., Dhuli, V. S., Enduri, M. K., Hajarathaiah, K., & Cenkeramaddi, L. R. 
(2024). Quantifying node influence in networks: Isolating-betweenness centrality for 
improved ranking. IEEE Access, 12, 93711–93722. https://doi.org/10.1109/ 
ACCESS.2024.3424834
Christakis, N. A., & Fowler, J. H. (2013). Social contagion theory: Examining dynamic 
social networks and human behavior. Statistics in Medicine, 32(4), 556–577. https:// 
doi.org/10.1002/sim.5408
Cialdini, R. B., & Goldstein, N. J. (2004). Social influence: Compliance and conformity. 
Annual Review of Psychology, 55(1), 591–621. https://doi.org/10.1146/annurev. 
psych.55.090902.142015
Dorogovtsev, S. N., Goltsev, A. V., & Mendes, J. F. F. (2006). K-core organization of 
complex networks. Physical Review Letters, 96(4), Article 040601. https://doi.org/ 
10.1103/PhysRevLett.96.040601
Fan, B., Shu, N., Li, Z., & Li, F. (2023). Critical nodes identification for power grid based 
on electrical topology and power flow distribution. IEEE Systems Journal, 17(3), 
4874–4884. https://doi.org/10.1109/JSYST.2022.3227632
Hajarathaiah, K., Enduri, M. K., Dhuli, S., Anamalamudi, S., & Cenkeramaddi, L. R. 
(2023). Generalization of relative change in a centrality measure to identify vital 
nodes in complex networks. IEEE Access, 11, 808–824. https://doi.org/10.1109/ 
ACCESS.2022.3232288
Isella, L., Stehl´e, J., Barrat, A., Cattuto, C., Pinton, J.-F., & Van Den Broeck, W. (2011). 
What’s in a crowd? Analysis of face-to-face behavioral networks. Journal of 
Theoretical Biology, 271(1), 166–180. https://doi.org/10.1016/j.jtbi.2010.11.033
Jeong, H., Mason, S. P., Barabasi, ´ A.-L., & Oltvai, Z. N. (2001). Lethality and centrality in 
protein networks. Nature, 411(6833), 41–42. https://doi.org/10.1038/35075138
Kendall, M. G. (1945). The treatment of ties in ranking problems. Biometrika, 33(3), 
239–251. https://doi.org/10.1093/biomet/33.3.239
Kitsak, M., Gallos, L. K., Havlin, S., Liljeros, F., Muchnik, L., Stanley, H. E., & 
Makse, H. A. (2010). Identification of influential spreaders in complex networks. 
Nature Physics, 6(11), 888–893. https://doi.org/10.1038/nphys1746
Knight, W. R. (1966). A computer method for calculating Kendall’s Tau with ungrouped 
data. Journal of the American Statistical Association, 61(314), 436–439. https://doi. 
org/10.1080/01621459.1966.10480879
Lancichinetti, A., Fortunato, S., & Radicchi, F. (2008). Benchmark graphs for testing 
community detection algorithms. Physical Review E, 78(4), Article 046110. https:// 
doi.org/10.1103/PhysRevE.78.046110
Li, H., Shang, Q., & Deng, Y. (2021). A generalized gravity model for influential spreaders 
identification in complex networks. Chaos, Solitons & Fractals, 143, Article 110456. 
https://doi.org/10.1016/j.chaos.2020.110456
Li, Z., Ren, T., Ma, X., Liu, S., Zhang, Y., & Zhou, T. (2019). Identifying influential 
spreaders by gravity model. Scientific Reports, 9(1), 8387. https://doi.org/10.1038/ 
s41598-019-44930-9
Lin, J.-H., Guo, Q., Dong, W.-Z., Tang, L.-Y., & Liu, J.-G. (2014). Identifying the node 
spreading influence with largest k-core values. Physics Letters A, 378(45), 
3279–3284. https://doi.org/10.1016/j.physleta.2014.09.054
Lü, L., Chen, D.-B., & Zhou, T. (2011). The small world yields the most effective 
information spreading. New Journal of Physics, 13(12), 1–10. https://doi.org/ 
10.1088/1367-2630/13/12/123005
Liu, F., Wang, Z., & Deng, Y. (2020). GMM: A generalized mechanics model for 
identifying the importance of nodes in complex networks. Knowledge-Based Systems, 
193, Article 105464. https://doi.org/10.1016/j.knosys.2019.105464
Liu, Y., Tang, M., Zhou, T., & Do, Y. (2015). Core-like groups result in invalidation of 
identifying super-spreader by k-shell decomposition. Scientific Reports, 5(1), 9602. 
https://doi.org/10.1038/srep09602
Lü, L., Zhou, T., Zhang, Q.-M., & Stanley, H. E. (2016). The H-index of a network node 
and its relation to degree and coreness. Nature Communications, 7(1), 10168. https:// 
doi.org/10.1038/ncomms10168
Ma, L., Ma, C., Zhang, H.-F., & Wang, B.-H. (2016). Identifying influential spreaders in 
complex networks based on gravity formula. Physica A: Statistical Mechanics and Its 
Applications, 451, 205–212. https://doi.org/10.1016/j.physa.2015.12.162
Mehdi Azaouzi;Lotfi Ben Romdhane. (2018). An efficient two-phase model for computing 
influential nodes in social networks using social actions. Journal of Computer Science 
& Technology, 33(2), 286–304. https://doi.org/10.1007/s11390-018-1820-9
Newman, M. E. J. (2002). Spread of epidemic disease on networks. Physical Review E, 66 
(1), Article 016128. https://doi.org/10.1103/PhysRevE.66.016128
Newman, M. E. J. (2003). The structure and function of complex networks. SIAM Review, 
45(2), 167–256. https://doi.org/10.1137/S003614450342480
Newman, M. E. J. (2006). Finding community structure in networks using the 
eigenvectors of matrices. Physical Review E, 74(3), Article 036104. https://doi.org/ 
10.1103/PhysRevE.74.036104
Pastor-Satorras, R., & Vespignanj, A. (2011). Epidemic spreading in scale-free networks. 
In I. M. Newman, A.-.-L. Barabasi, ´ & D. J. Watts (Eds.), The structure and dynamics of 
networks (pp. 493–496). Princeton University Press. https://doi.org/10.1515/ 
9781400841356.493. 
Qu, C., Zhan, X., Wang, G., Wu, J., & Zhang, Z. (2019). Temporal information gathering 
process for node ranking in time-varying networks. Chaos, 29(3), Article N.PAG-N. 
PAG. https://doi.org/10.1063/1.5086059
Rual, J.-F., Venkatesan, K., Hao, T., Hirozane-Kishikawa, T., Dricot, A., Li, N., 
Berriz, G. F., Gibbons, F. D., Dreze, M., Ayivi-Guedehoussou, N., Klitgord, N., 
Simon, C., Boxem, M., Milstein, S., Rosenberg, J., Goldberg, D. S., Zhang, L. V., 
Wong, S. L., Franklin, G., & Vidal, M. (2005). Towards a proteome-scale map of the 
human protein–protein interaction network. Nature, 437(7062), 1173–1178. 
https://doi.org/10.1038/nature04209
Ruan, Y.-R., Lao, S.-Y., Tang, J., Bai, L., Guo, Y.-M., & College of Systems Engineering, 
National University of Defense Technology, Changsha 410073, China. (2022). Node 
importance ranking method in complex network based on gravity method. Acta 
Physica Sinica, 71(17), 176401. https://doi.org/10.7498/aps.71.20220565.
Ruan, Y.-R., Lao, S.-Y., Xiao, Y.-D., Wang, J.-D., & Bai, L. (2016). Identifying influence of 
nodes in complex networks with coreness centrality: Decreasing the impact of 
densely local connection. Chinese Physics Letters, 33(2), 1. https://doi.org/10.1088/ 
0256-307X/33/2/028901
Shang, Q., Deng, Y., & Cheong, K. H. (2021). Identifying influential nodes in complex 
networks: Effective distance gravity model. Information Sciences, 577, 162–179. 
https://doi.org/10.1016/j.ins.2021.01.053
Tidke, B., Mehta, R., Dhanani, J., Tiwari, S., Trivedi, M., & Kohle, M. L. (2018). SIRIF: 
Supervised influence ranking based on influential network. Journal of Intelligent & 
Fuzzy Systems, 35(2), 1225–1237. https://doi.org/10.3233/JIFS-169667
Ullah, A., Wang, B., Sheng, J., Long, J., Khan, N., & Sun, Z. (2021). Identifying vital 
nodes from local and global perspectives in complex networks. Expert Systems with 
Applications, 186, Article 115778. https://doi.org/10.1016/j.eswa.2021.115778
Wang, F., Sun, Z., Gan, Q., Fan, A., Shi, H., & Hu, H. (2022). Influential node 
identification by aggregating local structure information. Physica A: Statistical 
Mechanics and Its Applications, 593, Article 126885. https://doi.org/10.1016/j. 
physa.2022.126885
Wen, T., & Jiang, W. (2019). Identifying influential nodes based on fuzzy local dimension 
in complex networks. Chaos, Solitons & Fractals, 119, 332–342. https://doi.org/ 
10.1016/j.chaos.2019.01.011
Xu, B., Wang, J., & Zhang, X. (2015). Conformity-based cooperation in online social 
networks: The effect of heterogeneous social influence. Chaos, Solitons & Fractals, 81, 
78–82. https://doi.org/10.1016/j.chaos.2015.08.019
Xu, G., & Dong, C. (2024). CAGM: A communicability-based adaptive gravity model for 
influential nodes identification in complex networks. Expert Systems with 
Applications, 235, Article 121154. https://doi.org/10.1016/j.eswa.2023.121154
Yang, X., & Xiao, F. (2021). An improved gravity model to identify influential nodes in 
complex networks based on k-shell method. Knowledge-Based Systems, 227, Article 
107198. https://doi.org/10.1016/j.knosys.2021.107198
Zeng, A., & Zhang, C.-J. (2013). Ranking spreaders by decomposing complex networks. 
Physics Letters A, 377(14), 1031–1035. https://doi.org/10.1016/j. 
physleta.2013.02.039
Zhang, Q., Shuai, B., & Lü, M. (2022). A novel method to identify influential nodes in 
complex networks based on gravity centrality. Information Sciences, 618, 98–117. 
https://doi.org/10.1016/j.ins.2022.10.070
Zhang, X., Zhu, J., Wang, Q., & Zhao, H. (2013). Identifying influential nodes in complex 
networks with community structure. Knowledge-Based Systems, 42, 74–84. https:// 
doi.org/10.1016/j.knosys.2013.01.017
Zhao, J., Wen, T., Jahanshahi, H., & Cheong, K. H. (2022). The random walk-based 
gravity model to identify influential nodes in complex networks. Information 
Sciences, 609, 1706–1720. https://doi.org/10.1016/j.ins.2022.07.084
Zhao, Z., Li, D., Sun, Y., Zhang, R., & Liu, J. (2023). Ranking influential spreaders based 
on both node k-shell and structural hole. Knowledge-Based Systems, 260, Article 
110163. https://doi.org/10.1016/j.knosys.2022.110163
Y. Ruan et al. Expert Systems With Applications 268 (2025) 126292
11