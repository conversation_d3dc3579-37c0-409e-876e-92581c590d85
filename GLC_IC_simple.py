"""
GLC算法 + IC模型 影响力计算

完全按照您的要求：
1. GLC算法给出节点评分排名（从高到低）
2. 选出前50个最高评分的节点作为种子集 seed_arr
3. 调用 mc_influence(G, seed_arr, p, NUM_SIMUS=1000) 函数
4. 计算这50个节点的IC实际传播影响力
5. 保存IC结果

参考eigenvector.py的实现方式

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import random
import time


def mc_influence(G, seed_arr, p, NUM_SIMUS=1000):
    """
    您提供的IC模型代码
    
    Args:
        G: NetworkX图对象
        seed_arr: 种子节点列表
        p: 传播概率
        NUM_SIMUS: 模拟次数
        
    Returns:
        平均影响力
    """
    print(f"\n对种子集开始影响力计算: {seed_arr}")
    inf = 0
    num_nodes = G.number_of_nodes()
    
    for r in range(NUM_SIMUS):
        active = set(seed_arr)
        new_active = set(seed_arr)
        
        while new_active:
            next_active = set()
            for node in new_active:
                neighbors = set(G.neighbors(node))
                for neighbor in neighbors:
                    if neighbor not in active:
                        if random.random() < p:
                            next_active.add(neighbor)
                            active.add(neighbor)
            new_active = next_active
        inf += len(active)
    
    return inf / NUM_SIMUS


def main():
    """
    主函数：GLC算法 + IC模型影响力计算
    """
    print("GLC算法 + IC模型 影响力计算")
    print("=" * 60)
    
    # 1. 加载网络
    print("1. 加载网络...")
    try:
        # 尝试加载真实网络文件
        glc_temp = GLCCentrality(nx.Graph())
        
        # 按优先级尝试加载网络文件
        network_files = [
            ("networks/karate.txt", "karate"),
            ("networks/blog-int.txt", "blog"),
            ("networks/netscience-int.txt", "netscience")
        ]
        
        graph = None
        network_name = None
        
        for filepath, name in network_files:
            try:
                graph = glc_temp.load_network_from_file(filepath)
                network_name = name
                print(f"成功加载网络文件: {filepath}")
                break
            except:
                continue
        
        if graph is None:
            # 如果所有文件都加载失败，使用内置网络
            print("网络文件未找到，使用Karate Club网络...")
            graph = nx.karate_club_graph()
            network_name = "karate_builtin"
    
    except Exception as e:
        print(f"加载网络时出错: {e}")
        print("使用Karate Club网络...")
        graph = nx.karate_club_graph()
        network_name = "karate_builtin"
    
    # 2. 显示网络信息
    print(f"\n网络基本信息:")
    print(f"  网络名称: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")
    
    # 3. 设置λ参数
    optimal_lambda_values = {
        'netscience': 1.0,
        'facebook': 0.95,
        'blog': 0.7,
        'karate': 0.5,
        'karate_builtin': 0.5
    }
    
    lambda_param = optimal_lambda_values.get(network_name, 0.8)
    print(f"  λ参数: {lambda_param}")
    
    # 4. 运行GLC算法
    print(f"\n2. 运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=lambda_param)
    glc.run_glc_algorithm()
    
    # 5. 获取节点评分排名（从高到低）
    print(f"\n3. 获取GLC节点评分排名...")
    """ 对图G中的所有节点进行排序。排序依据是每个节点的GLC中心性 降序排序"""
    sorted_nodes_by_glc = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    # 显示前10个节点的GLC值
    print(f"前10个节点的GLC评分:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i in range(min(10, len(sorted_nodes_by_glc))):
        node = sorted_nodes_by_glc[i]
        glc_value = glc.glc_values[node]
        print(f"{i+1:<4} {node:<6} {glc_value:<12.4f}")
    
    # 6. 选择前k个节点作为种子集
    k = min(50, len(graph.nodes()))
    print(f"\n4. 选择前{k}个节点作为种子集...")
    """从已经根据GLC中心性分数排序的节点列表中选择前k个节点，作为种子节点集合seed_arr。"""
    seed_arr = sorted_nodes_by_glc[:k]
    
    print(f"种子集 seed_arr: {seed_arr}")
    
    # 7. 设置IC模型参数
    p = 0.05  # 传播概率
    NUM_SIMUS = 10000  # 模拟次数
    
    print(f"\n5. IC模型参数设置:")
    print(f"  种子节点数 k: {k}")
    print(f"  传播概率 p: {p}")
    print(f"  模拟次数: {NUM_SIMUS}")
    
    # 8. 计算IC影响力
    print(f"\n6. 计算IC影响力...")
    start_time = time.time()
    
    # 调用您的mc_influence函数
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    total_time = time.time() - start_time
    
    # 9. 显示结果
    print(f"\n" + "=" * 60)
    print(f"GLC算法 + IC模型 结果")
    print(f"=" * 60)
    print(f"网络: {network_name}")
    print(f"λ参数: {lambda_param}")
    print(f"种子节点数 k: {k}")
    print(f"种子集: {seed_arr}")
    print(f"IC影响力: {ic_influence:.4f}")
    print(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    print(f"=" * 60)
    
    # 10. 保存结果
    save_results(network_name, lambda_param, k, seed_arr, ic_influence, 
                 sorted_nodes_by_glc, glc.glc_values, graph, p, NUM_SIMUS)
    
    return ic_influence


def save_results(network_name, lambda_param, k, seed_arr, ic_influence, 
                sorted_nodes_by_glc, glc_values, graph, p, NUM_SIMUS):
    """
    保存IC结果到文件
    """
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"glc_ic_results_{network_name}_k{k}_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("GLC算法 + IC模型 影响力计算结果\n")
        f.write("=" * 60 + "\n")
        f.write(f"网络信息:\n")
        f.write(f"  网络名称: {network_name}\n")
        f.write(f"  节点数: {len(graph.nodes())}\n")
        f.write(f"  边数: {len(graph.edges())}\n")
        f.write(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}\n")
        f.write(f"  网络密度: {nx.density(graph):.4f}\n")
        f.write(f"\n")
        f.write(f"GLC算法参数:\n")
        f.write(f"  λ参数: {lambda_param}\n")
        f.write(f"\n")
        f.write(f"IC模型参数:\n")
        f.write(f"  种子节点数 k: {k}\n")
        f.write(f"  传播概率 p: {p}\n")
        f.write(f"  模拟次数: {NUM_SIMUS}\n")
        f.write(f"\n")
        f.write(f"结果:\n")
        f.write(f"  种子集: {seed_arr}\n")
        f.write(f"  IC影响力: {ic_influence:.4f}\n")
        f.write(f"  影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%\n")
        f.write(f"\n")
        
        # 写入GLC节点排名
        f.write("GLC节点排名 (前20个):\n")
        f.write("-" * 40 + "\n")
        f.write(f"{'排名':<4} {'节点':<6} {'GLC值':<12}\n")
        f.write("-" * 40 + "\n")
        
        for i in range(min(20, len(sorted_nodes_by_glc))):
            node = sorted_nodes_by_glc[i]
            glc_value = glc_values[node]
            f.write(f"{i+1:<4} {node:<6} {glc_value:<12.4f}\n")
        
        f.write(f"\n")
        f.write(f"说明:\n")
        f.write(f"- GLC算法选出前{k}个最重要节点作为种子集\n")
        f.write(f"- 使用IC模型计算种子集的实际传播影响力\n")
        f.write(f"- IC影响力表示在IC模型下平均能影响多少个节点\n")
    
    print(f"\n结果已保存到: {filename}")


if __name__ == "__main__":
    main()
