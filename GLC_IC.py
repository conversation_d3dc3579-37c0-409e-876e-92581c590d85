"""
GLC (Global-Local Centrality) 算法与IC模型结合实现

基于GLC.py的完整实现，保持所有GLC算法、λ*优化、Kendall tau评估等完全不变
唯一修改：在计算节点实际影响力时，将SIR模型替换为IC模型

与GLC.py保持完全一致的功能：
1. 群组检测和全局关键节点选择（Section 3.1）
2. 局部影响力计算（Section 3.2）
3. 整体影响力计算（Section 3.3）
4. Kendall tau相关系数计算（Section 4）
5. λ*参数优化（Section 6）

唯一修改：SIR模型 → IC模型（在evaluate_node_influence等方法中）

作者：基于GLC.py修改，保持论文思想完全一致
"""

import networkx as nx
import numpy as np
import random
import time
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple, Union, Optional
from scipy.stats import kendalltau
import matplotlib.pyplot as plt


class ICModel:
    """
    IC (Independent Cascade) 模型实现

    替换SIR模型，用于评估节点的传播影响力
    保持与SIR模型完全相同的接口，确保其他代码无需修改
    """

    def __init__(self, graph: nx.Graph, recovery_rate: float = 1.0):
        """
        初始化IC模型

        Args:
            graph: NetworkX图对象
            recovery_rate: 为了保持接口一致性，实际在IC模型中不使用
        """
        self.graph = graph
        self.recovery_rate = recovery_rate  # 保持接口一致，但IC模型不使用

    def calculate_epidemic_threshold(self) -> float:
        """
        计算流行病阈值（保持与SIR模型接口一致）

        对于IC模型，返回基于平均度数的阈值

        Returns:
            流行病阈值
        """
        degrees = [self.graph.degree(node) for node in self.graph.nodes()]
        avg_degree = sum(degrees) / len(degrees)
        # IC模型的阈值通常是1/<k>
        threshold = 1.0 / avg_degree if avg_degree > 0 else 0.01
        return threshold

    def mc_influence(self, seed_arr: List[int], p: float, NUM_SIMUS: int = 1000) -> float:
        """
        使用您提供的IC模型代码计算种子集的影响力

        Args:
            seed_arr: 种子节点列表
            p: 传播概率
            NUM_SIMUS: 模拟次数

        Returns:
            平均影响力
        """
        print(f"\n对种子集开始影响力计算: {seed_arr}")
        inf = 0
        num_nodes = self.graph.number_of_nodes()

        for r in range(NUM_SIMUS):
            active = set(seed_arr)
            new_active = set(seed_arr)

            while new_active:
                next_active = set()
                for node in new_active:
                    neighbors = set(self.graph.neighbors(node))
                    for neighbor in neighbors:
                        if neighbor not in active:
                            if random.random() < p:
                                next_active.add(neighbor)
                                active.add(neighbor)
                new_active = next_active
            inf += len(active)

        return inf / NUM_SIMUS

    def evaluate_node_influence(self, node: int, infection_rate: float,
                               num_simulations: int = 1000) -> float:
        """
        评估单个节点的传播影响力（使用您提供的IC模型代码）

        Args:
            node: 要评估的节点
            infection_rate: 传播概率
            num_simulations: 模拟次数M

        Returns:
            平均传播影响力
        """
        # 使用您提供的mc_influence函数，传入单个节点作为种子集
        return self.mc_influence([node], infection_rate, num_simulations)

    def evaluate_all_nodes(self, infection_rate: float,
                          num_simulations: int = 1000) -> Dict[int, float]:
        """
        评估所有节点的传播影响力（替换SIR评估）

        Args:
            infection_rate: 传播概率
            num_simulations: 模拟次数M

        Returns:
            节点到传播影响力的映射字典
        """
        node_influences = {}

        print(f"开始IC模拟评估 (p={infection_rate:.3f}, M={num_simulations})...")

        for i, node in enumerate(self.graph.nodes()):
            if (i + 1) % 50 == 0:
                print(f"  进度: {i + 1}/{len(self.graph.nodes())}")

            influence = self.evaluate_node_influence(node, infection_rate, num_simulations)
            node_influences[node] = influence

        print("IC模拟评估完成！")
        return node_influences


class GLCCentrality:
    """GLC中心性算法实现类"""

    def __init__(self, graph: nx.Graph, lambda_param: float = 0.8):
        """
        初始化GLC算法

        Args:
            graph: NetworkX图对象
            lambda_param: 聚类覆盖比例参数，默认0.8
        """
        self.graph = graph.copy()
        self.lambda_param = lambda_param
        self.clusters = []
        self.global_critical_nodes = set()
        self.pc_values = {}
        self.k_shell_values = {}
        self.local_influence = {}
        self.glc_values = {}

        # IC模型相关（替换SIR模型）
        self.ic_model = None
        self.epidemic_threshold = None

        # λ*优化相关
        self.lambda_optimization_results = {}
        self.optimal_lambda = None

    def load_network_from_file(self, filepath: str) -> nx.Graph:
        """
        从文件加载网络数据

        Args:
            filepath: 网络数据文件路径

        Returns:
            NetworkX图对象
        """
        graph = nx.Graph()

        with open(filepath, 'r') as f:
            lines = f.readlines()

        # 第一行通常包含节点数和边数信息
        first_line = lines[0].strip().split()
        if len(first_line) == 2:
            n_nodes, n_edges = map(int, first_line)
            lines = lines[1:]  # 跳过第一行

        # 读取边信息
        for line in lines:
            if line.strip():
                parts = line.strip().split()
                if len(parts) >= 2:
                    u, v = int(parts[0]), int(parts[1])
                    graph.add_edge(u, v)

        return graph

    def compute_k_shell(self) -> Dict[int, int]:
        """
        计算所有节点的k-shell值

        Returns:
            节点到k-shell值的映射字典
        """
        # 使用NetworkX内置的k-shell分解
        k_shell = nx.core_number(self.graph)
        self.k_shell_values = k_shell
        return k_shell

    def compute_clustering_potential(self) -> Dict[int, float]:
        """
        计算所有节点的聚类潜力pc值

        根据论文公式(10): pc_i = k_i * sum(k_in_j for j in N(i))

        重要修正：根据论文第318-320行的准确定义：
        k_in_j means "the number of links that connecting node j to the node i and i's neighbors"
        这意味着k_in_j是邻居节点j连接到{节点i + 节点i的邻居}这个集合的边数

        Returns:
            节点到pc值的映射字典
        """
        pc_values = {}

        for node in self.graph.nodes():
            k_i = self.graph.degree(node)  # 节点i的度数
            neighbors_i = set(self.graph.neighbors(node))  # 节点i的邻居集合

            # 计算sum(k_in_j for j in N(i))
            sum_k_in = 0
            for neighbor_j in neighbors_i:
                # k_in_j: 邻居节点j连接到"节点i和节点i的邻居"的边数
                neighbors_j = set(self.graph.neighbors(neighbor_j))
                # "节点i和节点i的邻居"的集合 = {i} ∪ N(i)
                i_and_its_neighbors = neighbors_i | {node}
                # 计算邻居j与{节点i和节点i的邻居}的连接数
                k_in_j = len(neighbors_j.intersection(i_and_its_neighbors))
                sum_k_in += k_in_j

            # 根据公式(10): pc_i = k_i * sum(k_in_j)
            pc_values[node] = k_i * sum_k_in

        self.pc_values = pc_values
        return pc_values

    def detect_clusters(self) -> List[Set[int]]:
        """
        检测网络中的聚类群组

        实现算法1中的聚类检测过程：
        1. 选择pc值最大的节点作为初始节点
        2. 添加pc值超过pcmax/2的邻居节点
        3. 通过三度影响规则扩展聚类
        4. 重复直到覆盖λ比例的节点

        Returns:
            聚类列表，每个聚类是节点集合
        """
        if not self.pc_values:
            self.compute_clustering_potential()

        clusters = []
        remaining_nodes = set(self.graph.nodes())
        pc_values_copy = self.pc_values.copy()
        total_nodes = len(self.graph.nodes())
        target_coverage = int(total_nodes * self.lambda_param)
        covered_nodes = 0

        while covered_nodes < target_coverage and remaining_nodes:
            # Step 1: 选择pc值最大的节点作为初始节点
            if not any(pc_values_copy[node] > 0 for node in remaining_nodes):
                break

            # 找到pc值最大的节点
            max_pc = max(pc_values_copy[node] for node in remaining_nodes if pc_values_copy[node] > 0)
            candidates = [node for node in remaining_nodes if pc_values_copy[node] == max_pc]

            # 在候选节点中选择度数最大的作为初始节点
            initial_node = max(candidates, key=lambda x: self.graph.degree(x))

            # 创建新聚类
            cluster = {initial_node}

            # 添加pc值超过pcmax/2的邻居节点
            threshold = max_pc / 2
            for neighbor in self.graph.neighbors(initial_node):
                if neighbor in remaining_nodes and pc_values_copy[neighbor] >= threshold:
                    cluster.add(neighbor)

            # Step 2: 通过三度影响规则扩展聚类（重复3次）
            for iteration in range(3):
                new_nodes = set()

                # 获取聚类的邻居节点
                cluster_neighbors = set()
                for node in cluster:
                    cluster_neighbors.update(self.graph.neighbors(node))
                cluster_neighbors -= cluster  # 移除已在聚类中的节点
                cluster_neighbors &= remaining_nodes  # 只考虑剩余节点

                # 按度数升序检查邻居节点
                sorted_neighbors = sorted(cluster_neighbors, key=lambda x: self.graph.degree(x))

                for neighbor in sorted_neighbors:
                    # 计算k_in和k_out
                    k_in = len(set(self.graph.neighbors(neighbor)) & cluster)
                    k_out = self.graph.degree(neighbor) - k_in

                    # 如果k_in >= k_out，则加入聚类
                    if k_in >= k_out:
                        new_nodes.add(neighbor)

                cluster.update(new_nodes)

            # Step 3: 将聚类中的节点pc值设为0
            for node in cluster:
                pc_values_copy[node] = 0

            clusters.append(cluster)
            remaining_nodes -= cluster
            covered_nodes += len(cluster)

        self.clusters = clusters
        return clusters

    def select_global_critical_nodes(self) -> Set[int]:
        """
        从每个聚类中选择度数最大的节点作为全局关键节点

        Returns:
            全局关键节点集合
        """
        if not self.clusters:
            self.detect_clusters()

        global_critical_nodes = set()

        for cluster in self.clusters:
            if cluster:  # 确保聚类非空
                # 选择聚类中度数最大的节点
                critical_node = max(cluster, key=lambda x: self.graph.degree(x))
                global_critical_nodes.add(critical_node)

        self.global_critical_nodes = global_critical_nodes
        return global_critical_nodes

    def compute_local_influence(self) -> Dict[int, float]:
        """
        计算所有节点的局部影响力

        根据公式: LI_i = sum(ks_j for j in N(i))
        其中ks_j是邻居节点j的k-shell值

        Returns:
            节点到局部影响力的映射字典
        """
        if not self.k_shell_values:
            self.compute_k_shell()

        local_influence = {}

        for node in self.graph.nodes():
            # 计算邻居节点的k-shell值之和
            li_value = sum(self.k_shell_values[neighbor] for neighbor in self.graph.neighbors(node))
            local_influence[node] = li_value

        self.local_influence = local_influence
        return local_influence

    def compute_glc_centrality(self) -> Dict[int, float]:
        """
        计算所有节点的GLC中心性值

        根据公式: GLC_i = LI_i * sum(LI_u / 2^d_iu for u in C)
        其中:
        - LI_i是节点i的局部影响力
        - C是全局关键节点集合
        - LI_u是全局关键节点u的局部影响力
        - d_iu是节点i到全局关键节点u的最短路径长度

        Returns:
            节点到GLC值的映射字典
        """
        # 确保所有必要的计算都已完成
        if not self.local_influence:
            self.compute_local_influence()
        if not self.global_critical_nodes:
            self.select_global_critical_nodes()

        glc_values = {}

        # 计算所有节点对之间的最短路径
        try:
            shortest_paths = dict(nx.all_pairs_shortest_path_length(self.graph))
        except:
            # 如果图不连通，使用单源最短路径
            shortest_paths = {}
            for node in self.graph.nodes():
                shortest_paths[node] = nx.single_source_shortest_path_length(self.graph, node)

        for node in self.graph.nodes():
            li_i = self.local_influence[node]

            # 计算全局影响力部分
            global_influence_sum = 0
            for critical_node in self.global_critical_nodes:
                li_u = self.local_influence[critical_node]

                # 获取最短路径长度
                if node in shortest_paths and critical_node in shortest_paths[node]:
                    d_iu = shortest_paths[node][critical_node]
                elif critical_node in shortest_paths and node in shortest_paths[critical_node]:
                    d_iu = shortest_paths[critical_node][node]
                else:
                    # 如果节点不连通，设置一个大的距离值
                    d_iu = float('inf')
                    continue

                if d_iu != float('inf') and d_iu > 0:
                    global_influence_sum += li_u / (2 ** d_iu)
                elif d_iu == 0:  # 节点本身就是全局关键节点
                    global_influence_sum += li_u

            # 计算最终的GLC值
            glc_values[node] = li_i * global_influence_sum

        self.glc_values = glc_values
        return glc_values

    def run_glc_algorithm(self) -> Dict[int, float]:
        """
        运行完整的GLC算法

        Returns:
            按GLC值排序的节点字典
        """
        print("开始运行GLC算法...")

        # Step 1: 计算k-shell值
        print("1. 计算k-shell值...")
        self.compute_k_shell()

        # Step 2: 计算聚类潜力
        print("2. 计算聚类潜力...")
        self.compute_clustering_potential()

        # Step 3: 检测聚类
        print("3. 检测聚类...")
        clusters = self.detect_clusters()
        print(f"   检测到 {len(clusters)} 个聚类")

        # Step 4: 选择全局关键节点
        print("4. 选择全局关键节点...")
        global_nodes = self.select_global_critical_nodes()
        print(f"   选择了 {len(global_nodes)} 个全局关键节点")

        # Step 5: 计算局部影响力
        print("5. 计算局部影响力...")
        self.compute_local_influence()

        # Step 6: 计算GLC中心性
        print("6. 计算GLC中心性...")
        glc_values = self.compute_glc_centrality()

        # 按GLC值排序
        sorted_nodes = sorted(glc_values.items(), key=lambda x: x[1], reverse=True)

        print("GLC算法运行完成！")
        return dict(sorted_nodes)

    def get_top_k_nodes(self, k: int = 10) -> List[Tuple[int, float]]:
        """
        获取前k个最重要的节点

        Args:
            k: 返回的节点数量

        Returns:
            (节点, GLC值)的列表
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        sorted_nodes = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
        return sorted_nodes[:k]

    def print_results(self, top_k: int = 10):
        """
        打印算法结果

        Args:
            top_k: 显示前k个重要节点
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        print(f"\n=== GLC算法结果 ===")
        print(f"网络节点数: {len(self.graph.nodes())}")
        print(f"网络边数: {len(self.graph.edges())}")
        print(f"检测到的聚类数: {len(self.clusters)}")
        print(f"全局关键节点数: {len(self.global_critical_nodes)}")

        # 显示聚类信息
        print(f"\n聚类详细信息:")
        for i, cluster in enumerate(self.clusters, 1):
            print(f"聚类 {i}: {len(cluster)} 个节点 - {sorted(list(cluster))}")

        print(f"\n全局关键节点: {sorted(list(self.global_critical_nodes))}")

        print(f"\n前 {top_k} 个最重要的节点:")
        print("排名\t节点ID\tGLC值\t\t度数\tk-shell值\t局部影响力")
        print("-" * 70)

        top_nodes = self.get_top_k_nodes(top_k)
        for i, (node, glc_value) in enumerate(top_nodes, 1):
            degree = self.graph.degree(node)
            k_shell = self.k_shell_values.get(node, 0)
            local_inf = self.local_influence.get(node, 0)
            print(f"{i}\t{node}\t{glc_value:.6f}\t{degree}\t{k_shell}\t\t{local_inf:.2f}")

    def compare_with_other_centralities(self, top_k: int = 10):
        """
        与其他中心性指标进行比较

        Args:
            top_k: 比较的节点数量
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        # 计算其他中心性指标
        degree_centrality = nx.degree_centrality(self.graph)
        betweenness_centrality = nx.betweenness_centrality(self.graph)
        closeness_centrality = nx.closeness_centrality(self.graph)
        eigenvector_centrality = nx.eigenvector_centrality(self.graph, max_iter=1000)

        # 获取各种中心性的前k个节点
        glc_top = [node for node, _ in self.get_top_k_nodes(top_k)]
        degree_top = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        degree_top = [node for node, _ in degree_top]
        betweenness_top = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        betweenness_top = [node for node, _ in betweenness_top]
        closeness_top = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        closeness_top = [node for node, _ in closeness_top]
        eigenvector_top = sorted(eigenvector_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        eigenvector_top = [node for node, _ in eigenvector_top]

        print(f"\n=== 中心性指标比较 (前{top_k}个节点) ===")
        print("排名\tGLC\t度中心性\t介数中心性\t接近中心性\t特征向量中心性")
        print("-" * 80)

        for i in range(top_k):
            glc_node = glc_top[i] if i < len(glc_top) else "-"
            degree_node = degree_top[i] if i < len(degree_top) else "-"
            betweenness_node = betweenness_top[i] if i < len(betweenness_top) else "-"
            closeness_node = closeness_top[i] if i < len(closeness_top) else "-"
            eigenvector_node = eigenvector_top[i] if i < len(eigenvector_top) else "-"

            print(f"{i+1}\t{glc_node}\t{degree_node}\t\t{betweenness_node}\t\t{closeness_node}\t\t{eigenvector_node}")

    def save_results_to_file(self, filename: str = "glc_results.txt"):
        """
        将结果保存到文件

        Args:
            filename: 输出文件名
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"检测到的聚类数: {len(self.clusters)}\n")
            f.write(f"全局关键节点数: {len(self.global_critical_nodes)}\n\n")

            f.write("所有节点的GLC值:\n")
            f.write("节点ID\tGLC值\t\t度数\tk-shell值\t局部影响力\n")
            f.write("-" * 60 + "\n")

            sorted_nodes = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
            for node, glc_value in sorted_nodes:
                degree = self.graph.degree(node)
                k_shell = self.k_shell_values.get(node, 0)
                local_inf = self.local_influence.get(node, 0)
                f.write(f"{node}\t{glc_value:.6f}\t{degree}\t{k_shell}\t\t{local_inf:.2f}\n")

        print(f"结果已保存到文件: {filename}")

    def analyze_network_properties(self):
        """
        分析网络的基本属性
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        print(f"\n=== 网络属性分析 ===")

        # 基本统计
        n_nodes = len(self.graph.nodes())
        n_edges = len(self.graph.edges())
        density = nx.density(self.graph)

        print(f"节点数: {n_nodes}")
        print(f"边数: {n_edges}")
        print(f"网络密度: {density:.4f}")

        # 度分布统计
        degrees = [self.graph.degree(node) for node in self.graph.nodes()]
        avg_degree = sum(degrees) / len(degrees)
        max_degree = max(degrees)
        min_degree = min(degrees)

        print(f"平均度数: {avg_degree:.2f}")
        print(f"最大度数: {max_degree}")
        print(f"最小度数: {min_degree}")

        # k-shell统计
        k_shells = list(self.k_shell_values.values())
        max_k_shell = max(k_shells)
        avg_k_shell = sum(k_shells) / len(k_shells)

        print(f"最大k-shell值: {max_k_shell}")
        print(f"平均k-shell值: {avg_k_shell:.2f}")

    def initialize_ic_model(self):
        """
        初始化IC模型（替换initialize_sir_model）
        """
        if self.ic_model is None:
            self.ic_model = ICModel(self.graph)
            self.epidemic_threshold = self.ic_model.calculate_epidemic_threshold()
            print(f"IC模型初始化完成，流行病阈值 = {self.epidemic_threshold:.4f}")

    def calculate_kendall_tau(self, ranking1: List[int], ranking2: List[int]) -> float:
        """
        计算两个排名之间的Kendall tau相关系数

        Args:
            ranking1: 第一个排名列表
            ranking2: 第二个排名列表

        Returns:
            Kendall tau相关系数
        """
        try:
            # 创建排名映射
            rank_map1 = {node: i for i, node in enumerate(ranking1)}
            rank_map2 = {node: i for i, node in enumerate(ranking2)}

            # 获取共同节点
            common_nodes = set(rank_map1.keys()) & set(rank_map2.keys())

            if len(common_nodes) < 2:
                return 0.0

            # 提取排名值
            ranks1 = [rank_map1[node] for node in common_nodes]
            ranks2 = [rank_map2[node] for node in common_nodes]

            # 计算Kendall tau
            tau, _ = kendalltau(ranks1, ranks2)
            return tau if not np.isnan(tau) else 0.0

        except Exception as e:
            print(f"计算Kendall tau时出错: {e}")
            return 0.0

    def evaluate_lambda_performance(self, lambda_val: float,
                                   infection_rates: List[float] = None,
                                   num_simulations: int = 100) -> float:
        """
        评估特定λ值的性能

        Args:
            lambda_val: 要评估的λ值
            infection_rates: 感染率列表，默认为βth±7%
            num_simulations: SIR模拟次数

        Returns:
            平均Kendall tau值<θ>
        """
        # 初始化IC模型（替换SIR模型）
        self.initialize_ic_model()

        # 设置感染率范围
        if infection_rates is None:
            beta_th = self.epidemic_threshold
            if beta_th <= 0.07:
                infection_rates = [i * 0.01 for i in range(1, 16)]  # 0.01 to 0.15
            else:
                # βth ± 7%
                beta_min = max(0.01, beta_th * 0.93)
                beta_max = beta_th * 1.07
                infection_rates = [beta_min + i * (beta_max - beta_min) / 14 for i in range(15)]

        print(f"\n评估λ = {lambda_val}...")

        # 使用指定λ值运行GLC算法
        original_lambda = self.lambda_param
        self.lambda_param = lambda_val

        # 重置算法状态
        self.clusters = []
        self.global_critical_nodes = set()
        self.pc_values = {}
        self.local_influence = {}
        self.glc_values = {}

        # 运行GLC算法
        self.run_glc_algorithm()

        # 获取GLC排名
        glc_ranking = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
        glc_node_ranking = [node for node, _ in glc_ranking]

        # 计算不同感染率下的Kendall tau
        tau_values = []

        for i, beta in enumerate(infection_rates):
            print(f"  感染率 {i+1}/{len(infection_rates)}: β = {beta:.4f}")

            # IC模拟获得真实影响力排名（替换SIR模拟）
            ic_influences = self.ic_model.evaluate_all_nodes(beta, num_simulations)
            ic_ranking = sorted(ic_influences.items(), key=lambda x: x[1], reverse=True)
            ic_node_ranking = [node for node, _ in ic_ranking]

            # 计算Kendall tau
            tau = self.calculate_kendall_tau(glc_node_ranking, ic_node_ranking)
            tau_values.append(tau)
            print(f"    Kendall tau: {tau:.4f}")

        # 计算平均Kendall tau
        avg_tau = sum(tau_values) / len(tau_values)

        # 恢复原始λ值
        self.lambda_param = original_lambda

        print(f"  平均Kendall tau <θ>: {avg_tau:.4f}")
        return avg_tau

    def optimize_lambda(self, lambda_range: List[float] = None,
                       infection_rates: List[float] = None,
                       num_simulations: int = 100,
                       save_results: bool = True) -> float:
        """
        优化λ*参数

        严格按照论文Section 6的方法实现λ*优化

        Args:
            lambda_range: λ值搜索范围，默认[0.05, 0.1, ..., 1.0]
            infection_rates: 感染率列表
            num_simulations: SIR模拟次数
            save_results: 是否保存结果

        Returns:
            最优λ*值
        """
        print("=" * 60)
        print("开始λ*参数优化 (基于论文Section 6方法)")
        print("=" * 60)

        # 设置λ搜索范围
        if lambda_range is None:
            lambda_range = [i * 0.05 for i in range(1, 21)]  # 0.05 to 1.0

        print(f"λ搜索范围: {lambda_range}")
        print(f"IC模拟次数: {num_simulations}")

        # 评估每个λ值的性能
        lambda_performance = {}

        for lambda_val in lambda_range:
            avg_tau = self.evaluate_lambda_performance(
                lambda_val, infection_rates, num_simulations
            )
            lambda_performance[lambda_val] = avg_tau

        # 找到最优λ*
        optimal_lambda = max(lambda_performance.items(), key=lambda x: x[1])[0]
        optimal_tau = lambda_performance[optimal_lambda]

        print("\n" + "=" * 60)
        print("λ*优化结果:")
        print("=" * 60)
        print(f"最优λ*: {optimal_lambda}")
        print(f"最大<θ>: {optimal_tau:.4f}")

        # 显示所有结果
        print("\n所有λ值的性能:")
        print("λ值\t<θ>值")
        print("-" * 20)
        for lambda_val in sorted(lambda_performance.keys()):
            tau_val = lambda_performance[lambda_val]
            marker = " *" if lambda_val == optimal_lambda else ""
            print(f"{lambda_val:.2f}\t{tau_val:.4f}{marker}")

        # 保存结果
        self.lambda_optimization_results = lambda_performance
        self.optimal_lambda = optimal_lambda

        if save_results:
            self.save_lambda_optimization_results()

        return optimal_lambda

    def save_lambda_optimization_results(self, filename: str = None):
        """
        保存λ*优化结果

        Args:
            filename: 输出文件名，默认自动生成
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"lambda_optimization_results_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法λ*参数优化结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"流行病阈值βth: {self.epidemic_threshold:.4f}\n")
            f.write(f"最优λ*: {self.optimal_lambda}\n")
            f.write(f"最大<θ>: {self.lambda_optimization_results[self.optimal_lambda]:.4f}\n\n")

            f.write("所有λ值的性能:\n")
            f.write("λ值\t<θ>值\n")
            f.write("-" * 20 + "\n")

            for lambda_val in sorted(self.lambda_optimization_results.keys()):
                tau_val = self.lambda_optimization_results[lambda_val]
                marker = " *" if lambda_val == self.optimal_lambda else ""
                f.write(f"{lambda_val:.2f}\t{tau_val:.4f}{marker}\n")

        print(f"λ*优化结果已保存到: {filename}")

    def plot_lambda_optimization(self, save_plot: bool = True):
        """
        绘制λ*优化结果图

        Args:
            save_plot: 是否保存图片
        """
        if not self.lambda_optimization_results:
            print("请先运行optimize_lambda()方法")
            return

        lambda_values = sorted(self.lambda_optimization_results.keys())
        tau_values = [self.lambda_optimization_results[lam] for lam in lambda_values]

        plt.figure(figsize=(10, 6))
        plt.plot(lambda_values, tau_values, 'b-o', linewidth=2, markersize=6)

        # 标记最优点
        if self.optimal_lambda:
            optimal_tau = self.lambda_optimization_results[self.optimal_lambda]
            plt.plot(self.optimal_lambda, optimal_tau, 'r*', markersize=15,
                    label=f'λ* = {self.optimal_lambda}')

        plt.xlabel('λ值')
        plt.ylabel('平均Kendall tau <θ>')
        plt.title('GLC算法λ*参数优化结果')
        plt.grid(True, alpha=0.3)
        plt.legend()

        if save_plot:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"lambda_optimization_plot_{timestamp}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"优化结果图已保存到: {filename}")

        plt.show()

    def get_paper_optimal_lambda(self, network_name: str) -> Optional[float]:
        """
        获取论文中给出的最优λ*值

        Args:
            network_name: 网络名称

        Returns:
            论文中的最优λ*值，如果未找到返回None
        """
        paper_lambda_values = {
            'netscience': 1.0,
            'facebook': 0.95,
            'infectious': 0.4,
            'yeast': 0.7,
            'protein': 0.05,
            'ca-grqc': 0.4
        }

        return paper_lambda_values.get(network_name.lower())

    def compare_with_paper_lambda(self, network_name: str):
        """
        与论文给出的λ*值进行比较

        Args:
            network_name: 网络名称
        """
        paper_lambda = self.get_paper_optimal_lambda(network_name)

        if paper_lambda is None:
            print(f"论文中未包含网络'{network_name}'的λ*值")
            return

        if not self.lambda_optimization_results:
            print("请先运行optimize_lambda()方法")
            return

        print(f"\n与论文λ*值比较 (网络: {network_name}):")
        print("-" * 40)
        print(f"论文λ*值: {paper_lambda}")
        print(f"计算λ*值: {self.optimal_lambda}")

        if paper_lambda in self.lambda_optimization_results:
            paper_tau = self.lambda_optimization_results[paper_lambda]
            optimal_tau = self.lambda_optimization_results[self.optimal_lambda]
            print(f"论文λ*的<θ>: {paper_tau:.4f}")
            print(f"计算λ*的<θ>: {optimal_tau:.4f}")
            print(f"性能差异: {optimal_tau - paper_tau:.4f}")
        else:
            print("论文λ*值不在搜索范围内")

    def evaluate_k_range_ic_influence(self, k_max: int = 50, p: float = 0.05,
                                     num_simulations: int = 10000,
                                     save_to_file: bool = True,
                                     filename: str = None) -> Dict[int, float]:
        """
        评估k=1到k_max范围内的IC模型影响力

        这是您需要的核心功能：
        1. 运行GLC算法获得节点排名
        2. 选择前k个节点作为种子集
        3. 使用IC模型计算实际影响力
        4. 保存结果到txt文件

        Args:
            k_max: 最大种子节点数，默认50
            p: 传播概率，默认0.05
            num_simulations: IC模拟次数，默认10000
            save_to_file: 是否保存到文件
            filename: 输出文件名，如果为None则自动生成

        Returns:
            k值到IC影响力的映射字典
        """
        print("=" * 80)
        print("GLC算法 + IC模型 k=1到50 影响力评估")
        print("=" * 80)
        print(f"参数设置:")
        print(f"  传播概率 p = {p}")
        print(f"  模拟次数 = {num_simulations}")
        print(f"  评估范围: k=1 到 k={k_max}")
        print("=" * 80)

        # 1. 确保GLC算法已运行
        if not self.glc_values:
            print("运行GLC算法...")
            self.run_glc_algorithm()

        # 2. 初始化IC模型
        if self.ic_model is None:
            self.initialize_ic_model()

        # 3. 获取GLC排名
        glc_ranking = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
        print(f"GLC算法完成，共计算了 {len(glc_ranking)} 个节点的GLC值")

        # 4. 检查k_max是否合理
        if k_max > len(glc_ranking):
            print(f"警告：k_max({k_max})大于网络节点数({len(glc_ranking)})，调整为{len(glc_ranking)}")
            k_max = len(glc_ranking)

        # 5. 对每个k值进行IC影响力评估
        k_ic_results = {}

        print(f"\n开始IC模型影响力评估...")
        start_time = time.time()

        for k in range(1, k_max + 1):
            print(f"\n{'='*20} 评估 k={k} {'='*20}")

            # 选择前k个GLC节点作为种子集
            seed_nodes = [node for node, _ in glc_ranking[:k]]

            print(f"种子节点集合: {seed_nodes}")

            # 使用IC模型计算影响力
            ic_influence = self.ic_model.mc_influence(seed_nodes, p, num_simulations)

            # 存储结果
            k_ic_results[k] = ic_influence

            print(f"k={k} 的IC影响力: {ic_influence:.4f}")

            # 显示进度
            if k % 10 == 0:
                elapsed_time = time.time() - start_time
                avg_time_per_k = elapsed_time / k
                remaining_k = k_max - k
                estimated_remaining_time = avg_time_per_k * remaining_k

                print(f"\n进度报告:")
                print(f"  已完成: {k}/{k_max} ({k/k_max*100:.1f}%)")
                print(f"  已用时间: {elapsed_time:.1f}秒")
                print(f"  预计剩余时间: {estimated_remaining_time:.1f}秒")

        total_time = time.time() - start_time
        print(f"\n{'='*80}")
        print(f"IC影响力评估完成！总用时: {total_time:.1f}秒")
        print(f"{'='*80}")

        # 6. 保存结果到文件
        if save_to_file:
            if filename is None:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"glc_ic_k_range_results_{timestamp}.txt"

            self._save_k_range_results(k_ic_results, filename, p, num_simulations, glc_ranking)

        return k_ic_results

    def _save_k_range_results(self, k_ic_results: Dict[int, float], filename: str,
                             p: float, num_simulations: int, glc_ranking: List[Tuple[int, float]]):
        """
        保存k范围IC影响力结果到文件

        Args:
            k_ic_results: k值到IC影响力的映射
            filename: 输出文件名
            p: 传播概率
            num_simulations: 模拟次数
            glc_ranking: GLC排名列表
        """
        with open(filename, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write("GLC算法 + IC模型 k=1到50 影响力评估结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络信息:\n")
            f.write(f"  节点数: {len(self.graph.nodes())}\n")
            f.write(f"  边数: {len(self.graph.edges())}\n")
            f.write(f"  平均度数: {sum(dict(self.graph.degree()).values()) / len(self.graph.nodes()):.2f}\n")
            f.write(f"  网络密度: {nx.density(self.graph):.4f}\n")
            f.write(f"\n")
            f.write(f"GLC算法参数:\n")
            f.write(f"  λ参数: {self.lambda_param}\n")
            f.write(f"  聚类数: {len(self.clusters)}\n")
            f.write(f"  全局关键节点数: {len(self.global_critical_nodes)}\n")
            f.write(f"\n")
            f.write(f"IC模型参数:\n")
            f.write(f"  传播概率 p: {p}\n")
            f.write(f"  模拟次数: {num_simulations}\n")
            f.write(f"  评估范围: k=1 到 k={max(k_ic_results.keys())}\n")
            f.write(f"\n")

            # 写入详细结果
            f.write("详细结果:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'k值':<4} {'种子节点集合':<30} {'IC影响力':<12} {'增长率(%)':<12}\n")
            f.write("-" * 60 + "\n")

            base_influence = k_ic_results[1] if 1 in k_ic_results else 1.0

            for k in sorted(k_ic_results.keys()):
                seed_nodes = [node for node, _ in glc_ranking[:k]]
                seed_str = str(seed_nodes) if len(seed_nodes) <= 5 else f"{seed_nodes[:5]}..."
                influence = k_ic_results[k]
                growth_rate = (influence / base_influence - 1) * 100 if base_influence > 0 else 0

                f.write(f"{k:<4} {seed_str:<30} {influence:<12.4f} {growth_rate:<12.1f}\n")

            # 写入统计信息
            influences = list(k_ic_results.values())
            max_influence = max(influences)
            min_influence = min(influences)
            avg_influence = sum(influences) / len(influences)

            f.write(f"\n")
            f.write(f"统计信息:\n")
            f.write(f"  最大影响力: {max_influence:.4f}\n")
            f.write(f"  最小影响力: {min_influence:.4f}\n")
            f.write(f"  平均影响力: {avg_influence:.4f}\n")
            f.write(f"  总体增长率: {(max_influence / min_influence - 1) * 100:.1f}%\n")

            # 写入GLC排名信息
            f.write(f"\n")
            f.write(f"GLC节点排名 (前20个):\n")
            f.write("-" * 40 + "\n")
            f.write(f"{'排名':<4} {'节点':<6} {'GLC值':<12}\n")
            f.write("-" * 40 + "\n")

            for i, (node, glc_value) in enumerate(glc_ranking[:20], 1):
                f.write(f"{i:<4} {node:<6} {glc_value:<12.4f}\n")

        print(f"\n结果已保存到文件: {filename}")
        print(f"文件包含完整的k=1到{max(k_ic_results.keys())}的IC影响力评估结果")


def main():
    """
    主函数：演示完整的GLC算法和λ*优化功能
    """
    print("GLC算法完整实现演示")
    print("=" * 50)

    # 创建示例网络或加载真实网络
    try:
        # 尝试加载真实网络
        glc = GLCCentrality(nx.Graph())
        graph = glc.load_network_from_file("networks/blog-int.txt")
        network_name = "blog"
        print(f"成功加载网络文件: {network_name}")
    except:
        # 如果文件不存在，创建示例网络
        print("网络文件未找到，创建示例网络...")
        graph = nx.karate_club_graph()
        network_name = "karate_demo"

    # 初始化GLC算法
    glc = GLCCentrality(graph, lambda_param=0.8)

    print(f"网络基本信息:")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")

    # 1. 运行基本GLC算法
    print("\n" + "=" * 50)
    print("1. 运行基本GLC算法")
    print("=" * 50)

    glc.run_glc_algorithm()
    glc.print_results(top_k=10)

    # 2. 进行λ*参数优化（简化版，减少计算时间）
    print("\n" + "=" * 50)
    print("2. λ*参数优化")
    print("=" * 50)

    # 使用较小的搜索范围和模拟次数进行演示
    lambda_range = [0.2, 0.4, 0.6, 0.8, 1.0]
    optimal_lambda = glc.optimize_lambda(
        lambda_range=lambda_range,
        num_simulations=50,  # 减少模拟次数以加快演示
        save_results=True
    )

    # 3. 与论文λ*值比较（如果适用）
    paper_lambda = glc.get_paper_optimal_lambda(network_name)
    if paper_lambda:
        glc.compare_with_paper_lambda(network_name)
    else:
        print(f"\n论文中未包含网络'{network_name}'的λ*值")

    # 4. 使用最优λ*重新运行算法
    print("\n" + "=" * 50)
    print("3. 使用最优λ*重新运行算法")
    print("=" * 50)

    glc_optimal = GLCCentrality(graph, lambda_param=optimal_lambda)
    glc_optimal.run_glc_algorithm()
    glc_optimal.print_results(top_k=10)

    # 5. 比较不同中心性指标
    print("\n" + "=" * 50)
    print("4. 中心性指标比较")
    print("=" * 50)

    glc_optimal.compare_with_other_centralities(top_k=10)

    print("\n演示完成！")
    print("详细结果已保存到相应的输出文件中。")


if __name__ == "__main__":
    main()