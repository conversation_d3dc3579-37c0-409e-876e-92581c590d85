"""
GLC算法与IC（Independent Cascade）模型结合的影响力传播评估

该模块实现了基于蒙特卡洛模拟的IC模型，用于评估GLC算法识别的关键节点
在实际信息传播中的影响力效果。

主要功能：
1. 蒙特卡洛模拟的IC模型实现
2. GLC算法与其他中心性指标的影响力传播比较
3. 不同传播概率下的性能评估
4. 结果可视化和分析

作者：基于GLC算法扩展实现
"""

import networkx as nx
import random
import numpy as np
import time
from typing import Dict, List, Set, Tuple, Union
from GLC import GLCCentrality


def mc_influence(G: nx.Graph, seed_arr: List[int], p: float, NUM_SIMUS: int = 1000) -> float:
    """
    蒙特卡洛模拟的IC（Independent Cascade）模型

    Args:
        G: NetworkX图对象
        seed_arr: 种子节点列表
        p: 传播概率
        NUM_SIMUS: 模拟次数，默认1000

    Returns:
        平均影响力（激活节点数）
    """
    print(f"\n对种子集开始影响力计算: {seed_arr}")
    inf = 0
    num_nodes = G.number_of_nodes()

    for r in range(NUM_SIMUS):
        # 初始化激活节点集合
        active = set(seed_arr)
        new_active = set(seed_arr)

        # 传播过程
        while new_active:
            next_active = set()
            for node in new_active:
                neighbors = set(G.neighbors(node))
                for neighbor in neighbors:
                    if neighbor not in active:
                        # 以概率p激活邻居节点
                        if random.random() < p:
                            next_active.add(neighbor)
                            active.add(neighbor)
            new_active = next_active

        inf += len(active)

    average_influence = inf / NUM_SIMUS
    print(f"平均影响力: {average_influence:.2f} 节点 ({average_influence/num_nodes:.2%})")
    return average_influence


class GLCInfluenceEvaluator:
    """GLC算法影响力传播评估器"""

    def __init__(self, graph: nx.Graph, k: int = 50):
        """
        初始化评估器

        Args:
            graph: NetworkX图对象
            k: 选择的种子节点数量，默认50
        """
        self.graph = graph.copy()
        self.k = k
        self.glc_algorithm = GLCCentrality(graph)
        self.centrality_methods = {}
        self.influence_results = {}
        self.k_range_results = {}  # 存储k=1到50的结果

    def compute_all_centralities(self):
        """计算所有中心性指标"""
        print("计算各种中心性指标...")

        # 1. GLC中心性
        print("1. 计算GLC中心性...")
        self.glc_algorithm.run_glc_algorithm()
        glc_values = self.glc_algorithm.glc_values
        glc_top_k = sorted(glc_values.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['GLC'] = [node for node, _ in glc_top_k]

        # 2. 度中心性
        print("2. 计算度中心性...")
        degree_centrality = nx.degree_centrality(self.graph)
        degree_top_k = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Degree'] = [node for node, _ in degree_top_k]

        # 3. 介数中心性
        print("3. 计算介数中心性...")
        betweenness_centrality = nx.betweenness_centrality(self.graph)
        betweenness_top_k = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Betweenness'] = [node for node, _ in betweenness_top_k]

        # 4. 接近中心性
        print("4. 计算接近中心性...")
        closeness_centrality = nx.closeness_centrality(self.graph)
        closeness_top_k = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Closeness'] = [node for node, _ in closeness_top_k]

        # 5. 特征向量中心性
        print("5. 计算特征向量中心性...")
        try:
            eigenvector_centrality = nx.eigenvector_centrality(self.graph, max_iter=1000)
            eigenvector_top_k = sorted(eigenvector_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
            self.centrality_methods['Eigenvector'] = [node for node, _ in eigenvector_top_k]
        except:
            print("   特征向量中心性计算失败，跳过...")

        # 6. PageRank
        print("6. 计算PageRank...")
        pagerank = nx.pagerank(self.graph)
        pagerank_top_k = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['PageRank'] = [node for node, _ in pagerank_top_k]

        # 7. k-shell（k-core）
        print("7. 计算k-shell中心性...")
        k_shell = nx.core_number(self.graph)
        k_shell_top_k = sorted(k_shell.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['K-Shell'] = [node for node, _ in k_shell_top_k]

        print(f"完成所有中心性指标计算，共{len(self.centrality_methods)}种方法")

    def evaluate_influence_spread(self, p_values: List[float] = [0.01, 0.05, 0.1, 0.2],
                                 num_simulations: int = 1000):
        """
        评估不同传播概率下的影响力传播效果

        Args:
            p_values: 传播概率列表
            num_simulations: 每次评估的模拟次数
        """
        if not self.centrality_methods:
            self.compute_all_centralities()

        print(f"\n开始影响力传播评估...")
        print(f"种子节点数: {self.k}")
        print(f"传播概率: {p_values}")
        print(f"模拟次数: {num_simulations}")
        print("=" * 80)

        # 初始化结果存储
        for method in self.centrality_methods:
            self.influence_results[method] = {}

        # 对每个传播概率进行评估
        for p in p_values:
            print(f"\n传播概率 p = {p}")
            print("-" * 50)

            for method_name, seed_nodes in self.centrality_methods.items():
                print(f"\n评估方法: {method_name}")
                start_time = time.time()

                # 计算影响力
                influence = mc_influence(self.graph, seed_nodes, p, num_simulations)

                end_time = time.time()
                print(f"计算时间: {end_time - start_time:.2f} 秒")

                # 存储结果
                self.influence_results[method_name][p] = influence

        print("\n影响力传播评估完成！")

    def evaluate_glc_k_range(self, k_max: int = 50, p_values: List[float] = [0.05, 0.1, 0.2],
                            num_simulations: int = 1000):
        """
        评估GLC算法在k=1到k_max范围内的影响力传播效果

        Args:
            k_max: 最大种子节点数，默认50
            p_values: 传播概率列表
            num_simulations: 每次评估的模拟次数
        """
        # 确保GLC算法已运行
        if not self.glc_algorithm.glc_values:
            print("运行GLC算法...")
            self.glc_algorithm.run_glc_algorithm()

        # 获取按GLC值排序的节点列表
        glc_sorted_nodes = sorted(self.glc_algorithm.glc_values.items(),
                                 key=lambda x: x[1], reverse=True)

        print(f"\n开始评估GLC算法k=1到{k_max}的影响力传播...")
        print(f"传播概率: {p_values}")
        print(f"模拟次数: {num_simulations}")
        print("=" * 80)

        # 初始化结果存储
        for p in p_values:
            self.k_range_results[p] = {}

        # 对每个k值进行评估
        for k in range(1, k_max + 1):
            print(f"\n评估k={k}...")

            # 获取前k个GLC节点
            top_k_nodes = [node for node, _ in glc_sorted_nodes[:k]]

            # 对每个传播概率进行测试
            for p in p_values:
                print(f"  传播概率p={p}: ", end="")

                # 计算影响力
                influence = mc_influence(self.graph, top_k_nodes, p, num_simulations)

                # 存储结果
                self.k_range_results[p][k] = influence

                print(f"影响力={influence:.2f}")

        print(f"\nGLC算法k=1到{k_max}评估完成！")
        return self.k_range_results

    def print_comparison_results(self):
        """打印比较结果"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        print(f"\n{'='*80}")
        print("影响力传播比较结果")
        print(f"{'='*80}")

        # 获取所有传播概率
        p_values = list(next(iter(self.influence_results.values())).keys())

        # 打印表头
        header = "方法名称\t\t"
        for p in p_values:
            header += f"p={p}\t\t"
        print(header)
        print("-" * 80)

        # 打印每种方法的结果
        for method_name in self.influence_results:
            row = f"{method_name:<15}\t"
            for p in p_values:
                influence = self.influence_results[method_name][p]
                row += f"{influence:.2f}\t\t"
            print(row)

        # 找出每个概率下的最佳方法
        print(f"\n{'='*50}")
        print("最佳方法总结")
        print(f"{'='*50}")

        for p in p_values:
            best_method = max(self.influence_results.keys(),
                            key=lambda x: self.influence_results[x][p])
            best_influence = self.influence_results[best_method][p]
            print(f"p={p}: {best_method} (影响力: {best_influence:.2f})")

    def analyze_glc_performance(self):
        """分析GLC算法的性能表现"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        print(f"\n{'='*60}")
        print("GLC算法性能分析")
        print(f"{'='*60}")

        p_values = list(self.influence_results['GLC'].keys())

        for p in p_values:
            print(f"\n传播概率 p = {p}:")
            glc_influence = self.influence_results['GLC'][p]

            # 计算相对于其他方法的性能
            better_count = 0
            total_methods = len(self.influence_results) - 1  # 除了GLC本身

            for method_name, results in self.influence_results.items():
                if method_name != 'GLC':
                    other_influence = results[p]
                    improvement = (glc_influence - other_influence) / other_influence * 100
                    if glc_influence > other_influence:
                        better_count += 1
                        print(f"  vs {method_name}: +{improvement:.1f}% (GLC更好)")
                    else:
                        print(f"  vs {method_name}: {improvement:.1f}% (GLC较差)")

            print(f"  GLC胜出率: {better_count}/{total_methods} ({better_count/total_methods:.1%})")

    def save_results_to_file(self, filename: str = "glc_ic_results.txt"):
        """保存结果到文件"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法IC模型影响力传播评估结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"种子节点数: {self.k}\n\n")

            # 写入详细结果
            p_values = list(next(iter(self.influence_results.values())).keys())

            f.write("详细结果:\n")
            f.write("方法名称\t\t")
            for p in p_values:
                f.write(f"p={p}\t\t")
            f.write("\n")
            f.write("-" * 80 + "\n")

            for method_name in self.influence_results:
                f.write(f"{method_name:<15}\t")
                for p in p_values:
                    influence = self.influence_results[method_name][p]
                    f.write(f"{influence:.2f}\t\t")
                f.write("\n")

            # 写入最佳方法总结
            f.write(f"\n最佳方法总结:\n")
            for p in p_values:
                best_method = max(self.influence_results.keys(),
                                key=lambda x: self.influence_results[x][p])
                best_influence = self.influence_results[best_method][p]
                f.write(f"p={p}: {best_method} (影响力: {best_influence:.2f})\n")

        print(f"结果已保存到文件: {filename}")

    def export_to_csv(self, filename: str = "glc_ic_results.csv"):
        """导出结果为CSV格式"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 获取传播概率列表
            p_values = list(next(iter(self.influence_results.values())).keys())

            # 写入表头
            header = ['方法名称'] + [f'p={p}' for p in p_values]
            writer.writerow(header)

            # 写入数据
            for method_name in self.influence_results:
                row = [method_name]
                for p in p_values:
                    influence = self.influence_results[method_name][p]
                    row.append(f"{influence:.2f}")
                writer.writerow(row)

        print(f"结果已导出为CSV文件: {filename}")

    def save_k_range_results(self, filename: str = "glc_k_range_results.txt"):
        """
        保存k=1到50范围的结果到文件

        Args:
            filename: 输出文件名
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法k=1到50影响力传播结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n\n")

            # 获取传播概率列表
            p_values = list(self.k_range_results.keys())

            # 写入表头
            f.write("k值\t")
            for p in p_values:
                f.write(f"p={p}\t\t")
            f.write("\n")
            f.write("-" * 60 + "\n")

            # 写入数据
            k_max = max(max(self.k_range_results[p].keys()) for p in p_values)
            for k in range(1, k_max + 1):
                f.write(f"{k}\t")
                for p in p_values:
                    if k in self.k_range_results[p]:
                        influence = self.k_range_results[p][k]
                        f.write(f"{influence:.2f}\t\t")
                    else:
                        f.write("N/A\t\t")
                f.write("\n")

        print(f"k范围结果已保存到文件: {filename}")

    def export_k_range_to_csv(self, filename: str = "glc_k_range_results.csv"):
        """
        将k范围结果导出为CSV格式

        Args:
            filename: CSV文件名
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 获取传播概率列表
            p_values = list(self.k_range_results.keys())

            # 写入表头
            header = ['k'] + [f'p={p}' for p in p_values]
            writer.writerow(header)

            # 写入数据
            k_max = max(max(self.k_range_results[p].keys()) for p in p_values)
            for k in range(1, k_max + 1):
                row = [k]
                for p in p_values:
                    if k in self.k_range_results[p]:
                        influence = self.k_range_results[p][k]
                        row.append(f"{influence:.2f}")
                    else:
                        row.append("N/A")
                writer.writerow(row)

        print(f"k范围结果已导出为CSV文件: {filename}")

    def print_k_range_summary(self, k_values: List[int] = [1, 5, 10, 20, 30, 40, 50]):
        """
        打印k范围结果的摘要

        Args:
            k_values: 要显示的k值列表
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        print(f"\n{'='*60}")
        print("GLC算法k范围影响力传播摘要")
        print(f"{'='*60}")

        # 获取传播概率列表
        p_values = list(self.k_range_results.keys())

        # 打印表头
        header = "k值\t"
        for p in p_values:
            header += f"p={p}\t\t"
        print(header)
        print("-" * 60)

        # 打印指定k值的结果
        for k in k_values:
            if all(k in self.k_range_results[p] for p in p_values):
                row = f"{k}\t"
                for p in p_values:
                    influence = self.k_range_results[p][k]
                    row += f"{influence:.2f}\t\t"
                print(row)

        # 打印增长率分析
        print(f"\n增长率分析 (相对于k=1):")
        print("k值\t", end="")
        for p in p_values:
            print(f"p={p}增长率\t", end="")
        print()
        print("-" * 60)

        for k in [5, 10, 20, 30, 40, 50]:
            if all(k in self.k_range_results[p] and 1 in self.k_range_results[p] for p in p_values):
                print(f"{k}\t", end="")
                for p in p_values:
                    base_influence = self.k_range_results[p][1]
                    current_influence = self.k_range_results[p][k]
                    if base_influence > 0:
                        growth_rate = (current_influence / base_influence - 1) * 100
                        print(f"+{growth_rate:.1f}%\t\t", end="")
                    else:
                        print("N/A\t\t", end="")
                print()


def test_different_networks():
    """测试不同网络上的GLC-IC性能"""

    networks = [
        # ("networks/karate.txt", "Karate Club", 10),
        ("networks/blog-int.txt", "Blog Network", 50),
        ("networks/AS733.txt", "Power Grid", 50),
    ]

    results_summary = []

    for network_file, network_name, k_value in networks:
        try:
            print(f"\n{'='*80}")
            print(f"测试网络: {network_name}")
            print(f"文件: {network_file}")
            print(f"种子节点数: {k_value}")
            print('='*80)

            # 创建评估器
            evaluator = GLCInfluenceEvaluator(nx.Graph(), k=k_value)

            # 加载网络
            graph = evaluator.glc_algorithm.load_network_from_file(network_file)
            evaluator.graph = graph
            evaluator.glc_algorithm.graph = graph

            # 调整k值以适应网络大小
            max_k = min(k_value, len(graph.nodes()) // 3)
            evaluator.k = max_k

            print(f"网络规模: {len(graph.nodes())} 节点, {len(graph.edges())} 边")
            print(f"实际种子节点数: {max_k}")

            # 计算中心性指标
            evaluator.compute_all_centralities()

            # 评估影响力传播（使用较少的模拟次数）
            evaluator.evaluate_influence_spread(
                p_values=[0.05],
                num_simulations=200
            )

            # 分析GLC性能
            evaluator.analyze_glc_performance()

            # 保存结果
            filename = f"{network_name.lower().replace(' ', '_')}_glc_ic_results.txt"
            evaluator.save_results_to_file(filename)

            # 收集总结信息
            p_values = list(evaluator.influence_results['GLC'].keys())
            glc_performance = {}
            for p in p_values:
                glc_influence = evaluator.influence_results['GLC'][p]
                glc_performance[p] = glc_influence

            results_summary.append({
                'network': network_name,
                'nodes': len(graph.nodes()),
                'edges': len(graph.edges()),
                'k': max_k,
                'glc_performance': glc_performance
            })

        except FileNotFoundError:
            print(f"文件 {network_file} 不存在，跳过...")
        except Exception as e:
            print(f"处理 {network_name} 时出错: {e}")

    # 打印总结
    print(f"\n{'='*80}")
    print("所有网络GLC-IC测试总结")
    print('='*80)

    for result in results_summary:
        print(f"\n网络: {result['network']}")
        print(f"规模: {result['nodes']} 节点, {result['edges']} 边")
        print(f"种子数: {result['k']}")
        print("GLC影响力表现:")
        for p, influence in result['glc_performance'].items():
            coverage = influence / result['nodes'] * 100
            print(f"  p={p}: {influence:.1f} 节点 ({coverage:.1f}% 覆盖率)")


def main():
    """主函数，演示GLC-IC评估的使用"""

    # 加载网络数据
    print("加载网络数据...")
    evaluator = GLCInfluenceEvaluator(nx.Graph(), k=50)

    # 使用blog网络进行演示
    graph = evaluator.glc_algorithm.load_network_from_file("networks/blog-int.txt")
    evaluator.graph = graph
    evaluator.glc_algorithm.graph = graph

    # 固定k=50，不再调整
    evaluator.k = 50

    print(f"加载完成：节点数 = {len(graph.nodes())}, 边数 = {len(graph.edges())}")
    print(f"种子节点数固定为: {evaluator.k}")

    # 评估GLC算法k=1到50的影响力传播
    evaluator.evaluate_glc_k_range(
        k_max=50,
        p_values=[0.05, 0.1, 0.2],
        num_simulations=500
    )

    # 打印k范围结果摘要
    evaluator.print_k_range_summary()

    # 保存k范围结果
    evaluator.save_k_range_results("blog_glc_k_range_results.txt")
    evaluator.export_k_range_to_csv("blog_glc_k_range_results.csv")

    return evaluator


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行多网络测试
        test_different_networks()
    else:
        # 运行单个网络示例
        main()