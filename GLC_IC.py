"""
GLC算法与IC（Independent Cascade）模型结合的影响力传播评估

基于修正后的GLC.py实现，使用IC模型进行k=1到50的影响力传播评估
严格按照论文要求，使用蒙特卡洛模拟10000次，传播概率p=0.05

主要功能：
1. 基于GLC.py的完整GLC算法
2. IC模型蒙特卡洛模拟（10000次）
3. k=1到50的完整评估
4. 传播概率p=0.05的标准实验
5. 详细的结果分析和保存

作者：基于GLC.py修正实现
"""

import networkx as nx
import random
import numpy as np
import time
import csv
from typing import Dict, List, Set, Tuple, Union
from GLC import GLCCentrality


class ICModel:
    """
    IC (Independent Cascade) 模型实现

    专门用于GLC算法的影响力传播评估，支持高效的蒙特卡洛模拟
    """

    def __init__(self, graph: nx.Graph):
        """
        初始化IC模型

        Args:
            graph: NetworkX图对象
        """
        self.graph = graph

    def simulate_single_ic(self, seed_nodes: List[int], p: float) -> int:
        """
        单次IC模拟

        Args:
            seed_nodes: 种子节点列表
            p: 传播概率

        Returns:
            激活的节点数量
        """
        if not seed_nodes:
            return 0

        # 初始化激活节点集合
        active = set(seed_nodes)
        new_active = set(seed_nodes)

        # IC模型传播过程
        while new_active:
            next_active = set()
            for node in new_active:
                for neighbor in self.graph.neighbors(node):
                    if neighbor not in active:
                        # 以概率p激活邻居节点
                        if random.random() < p:
                            next_active.add(neighbor)
                            active.add(neighbor)
            new_active = next_active

        return len(active)

    def evaluate_influence(self, seed_nodes: List[int], p: float,
                          num_simulations: int = 10000) -> float:
        """
        评估种子节点的影响力

        Args:
            seed_nodes: 种子节点列表
            p: 传播概率
            num_simulations: 模拟次数，默认10000

        Returns:
            平均影响力
        """
        total_influence = 0

        for _ in range(num_simulations):
            influence = self.simulate_single_ic(seed_nodes, p)
            total_influence += influence

        return total_influence / num_simulations


class GLCICEvaluator:
    """
    GLC算法与IC模型结合的影响力传播评估器

    基于修正后的GLC.py实现，专门用于IC模型的k=1到50评估
    """

    def __init__(self, graph: nx.Graph, network_type: str = "unknown",
                 lambda_param: float = None):
        """
        初始化评估器

        Args:
            graph: NetworkX图对象
            network_type: 网络类型，用于设置λ参数
            lambda_param: 手动指定的λ参数，如果为None则根据网络类型自动设置
        """
        self.graph = graph.copy()
        self.network_type = network_type

        # 设置λ参数
        if lambda_param is None:
            lambda_param = self._get_optimal_lambda(network_type)

        # 初始化GLC算法
        self.glc_algorithm = GLCCentrality(graph, lambda_param=lambda_param)

        # 初始化IC模型
        self.ic_model = ICModel(graph)

        # 结果存储
        self.k_range_results = {}  # 存储k=1到50的结果
        self.glc_computed = False

        print(f"初始化完成：")
        print(f"  网络类型: {network_type}")
        print(f"  λ参数: {lambda_param}")
        print(f"  节点数: {len(graph.nodes())}")
        print(f"  边数: {len(graph.edges())}")

    def _get_optimal_lambda(self, network_type: str) -> float:
        """
        根据网络类型获取最优λ参数

        Args:
            network_type: 网络类型

        Returns:
            最优λ值
        """
        # 论文中已实验确定的网络最优λ*值
        paper_lambda_values = {
            'netscience': 1.0,
            'facebook': 0.95,
            'infectious': 0.4,
            'yeast': 0.7,
            'protein': 0.05,
            'ca-grqc': 0.4,
            'blog': 0.7,
            'karate': 0.5,
            'unknown': 0.8
        }

        lambda_val = paper_lambda_values.get(network_type.lower(), 0.8)

        if network_type.lower() in paper_lambda_values:
            print(f"  使用论文最优λ*值: {lambda_val} (网络: {network_type})")
        else:
            print(f"  使用默认λ值: {lambda_val} (未知网络类型: {network_type})")

        return lambda_val

    def run_glc_algorithm(self):
        """
        运行GLC算法
        """
        if not self.glc_computed:
            print("\n运行GLC算法...")
            self.glc_algorithm.run_glc_algorithm()
            self.glc_computed = True
            print(f"GLC算法完成，计算了 {len(self.glc_algorithm.glc_values)} 个节点的GLC值")

    def evaluate_k_range_ic(self, k_max: int = 50, p: float = 0.05,
                           num_simulations: int = 10000):
        """
        评估GLC算法在k=1到k_max范围内的IC模型影响力传播

        Args:
            k_max: 最大种子节点数，默认50
            p: 传播概率，默认0.05
            num_simulations: 蒙特卡洛模拟次数，默认10000
        """
        print("=" * 80)
        print("GLC算法 IC模型 k=1到50 影响力传播评估")
        print("=" * 80)
        print(f"网络类型: {self.network_type}")
        print(f"λ参数: {self.glc_algorithm.lambda_param}")
        print(f"传播概率: {p}")
        print(f"模拟次数: {num_simulations}")
        print(f"评估范围: k=1 到 k={k_max}")
        print("=" * 80)

        # 确保GLC算法已运行
        self.run_glc_algorithm()

        # 获取按GLC值排序的节点列表
        glc_ranking = sorted(self.glc_algorithm.glc_values.items(),
                           key=lambda x: x[1], reverse=True)

        if len(glc_ranking) < k_max:
            print(f"警告：网络节点数({len(glc_ranking)})小于k_max({k_max})")
            k_max = len(glc_ranking)

        # 初始化结果存储
        self.k_range_results[p] = {}

        print(f"\n开始IC模型评估...")
        start_time = time.time()

        # 对每个k值进行评估
        for k in range(1, k_max + 1):
            print(f"\n评估 k={k}...")

            # 获取前k个GLC节点
            top_k_nodes = [node for node, _ in glc_ranking[:k]]

            # 验证包含性（k=20包含k=10的所有节点）
            if k > 1:
                prev_nodes = set([node for node, _ in glc_ranking[:k-1]])
                curr_nodes = set(top_k_nodes)
                if not prev_nodes.issubset(curr_nodes):
                    raise ValueError(f"k={k}的节点集合不包含k={k-1}的节点集合")

            # IC模型影响力评估
            influence = self.ic_model.evaluate_influence(top_k_nodes, p, num_simulations)

            # 存储结果
            self.k_range_results[p][k] = influence

            # 计算增长率
            if k == 1:
                base_influence = influence
                growth_rate = 0.0
            else:
                growth_rate = (influence / base_influence - 1) * 100

            print(f"  前{k}个节点: {top_k_nodes}")
            print(f"  IC影响力: {influence:.2f}")
            print(f"  增长率: {growth_rate:.1f}%")

            # 每10个k值显示进度
            if k % 10 == 0:
                elapsed_time = time.time() - start_time
                avg_time_per_k = elapsed_time / k
                remaining_k = k_max - k
                estimated_remaining_time = avg_time_per_k * remaining_k

                print(f"\n进度: {k}/{k_max} ({k/k_max*100:.1f}%)")
                print(f"已用时间: {elapsed_time:.1f}秒")
                print(f"预计剩余时间: {estimated_remaining_time:.1f}秒")

        total_time = time.time() - start_time
        print(f"\n评估完成！总用时: {total_time:.1f}秒")

        # 验证结果的单调性
        self._validate_monotonicity(p)

        return self.k_range_results[p]

    def _validate_monotonicity(self, p: float):
        """
        验证影响力的单调性（影响力应该随k增加而增加或保持不变）

        Args:
            p: 传播概率
        """
        if p not in self.k_range_results:
            return

        k_values = sorted(self.k_range_results[p].keys())
        violations = 0

        for i in range(1, len(k_values)):
            curr_k = k_values[i]
            prev_k = k_values[i-1]
            curr_inf = self.k_range_results[p][curr_k]
            prev_inf = self.k_range_results[p][prev_k]

            if curr_inf < prev_inf - 0.01:  # 允许小的数值误差
                violations += 1
                print(f"单调性违反: k={curr_k}({curr_inf:.2f}) < k={prev_k}({prev_inf:.2f})")

        if violations == 0:
            print("✅ 单调性验证通过：影响力随k值单调递增")
        else:
            print(f"⚠️  发现{violations}个单调性违反（可能由随机性引起）")

    def print_k_range_summary(self, p: float = 0.05):
        """
        打印k范围评估结果摘要

        Args:
            p: 传播概率
        """
        if p not in self.k_range_results:
            print("请先运行evaluate_k_range_ic()方法")
            return

        results = self.k_range_results[p]
        k_values = sorted(results.keys())

        print("\n" + "=" * 80)
        print("GLC算法 IC模型 k=1到50 评估结果摘要")
        print("=" * 80)
        print(f"网络类型: {self.network_type}")
        print(f"λ参数: {self.glc_algorithm.lambda_param}")
        print(f"传播概率: {p}")
        print(f"评估范围: k={k_values[0]} 到 k={k_values[-1]}")

        # 关键k值的结果
        key_k_values = [1, 5, 10, 20, 30, 40, 50]
        key_k_values = [k for k in key_k_values if k in results]

        print(f"\n关键k值的IC影响力:")
        print("k值\tIC影响力\t增长率")
        print("-" * 30)

        base_influence = results[1]
        for k in key_k_values:
            influence = results[k]
            growth_rate = (influence / base_influence - 1) * 100
            print(f"{k}\t{influence:.2f}\t\t{growth_rate:.1f}%")

        # 统计信息
        influences = list(results.values())
        max_influence = max(influences)
        min_influence = min(influences)
        avg_influence = sum(influences) / len(influences)

        print(f"\n统计信息:")
        print(f"最大影响力: {max_influence:.2f} (k={k_values[influences.index(max_influence)]})")
        print(f"最小影响力: {min_influence:.2f} (k=1)")
        print(f"平均影响力: {avg_influence:.2f}")
        print(f"总体增长率: {(max_influence / min_influence - 1) * 100:.1f}%")

    def save_results_to_csv(self, filename: str = None, p: float = 0.05):
        """
        保存结果到CSV文件

        Args:
            filename: 输出文件名
            p: 传播概率
        """
        if p not in self.k_range_results:
            print("请先运行evaluate_k_range_ic()方法")
            return

        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"glc_ic_k_range_results_{self.network_type}_{timestamp}.csv"

        results = self.k_range_results[p]
        k_values = sorted(results.keys())

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            writer.writerow(['k值', 'IC影响力', '增长率(%)', '网络类型', 'λ参数', '传播概率'])

            # 写入数据
            base_influence = results[1]
            for k in k_values:
                influence = results[k]
                growth_rate = (influence / base_influence - 1) * 100
                writer.writerow([k, influence, growth_rate, self.network_type,
                               self.glc_algorithm.lambda_param, p])

        print(f"结果已保存到: {filename}")

    def save_results_to_txt(self, filename: str = None, p: float = 0.05):
        """
        保存详细结果到文本文件

        Args:
            filename: 输出文件名
            p: 传播概率
        """
        if p not in self.k_range_results:
            print("请先运行evaluate_k_range_ic()方法")
            return

        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"glc_ic_detailed_results_{self.network_type}_{timestamp}.txt"

        results = self.k_range_results[p]
        k_values = sorted(results.keys())

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法 IC模型 k=1到50 详细评估结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络类型: {self.network_type}\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"λ参数: {self.glc_algorithm.lambda_param}\n")
            f.write(f"传播概率: {p}\n")
            f.write(f"评估范围: k={k_values[0]} 到 k={k_values[-1]}\n")
            f.write(f"GLC聚类数: {len(self.glc_algorithm.clusters)}\n")
            f.write(f"全局关键节点数: {len(self.glc_algorithm.global_critical_nodes)}\n\n")

            f.write("详细结果:\n")
            f.write("k值\tIC影响力\t增长率(%)\n")
            f.write("-" * 40 + "\n")

            base_influence = results[1]
            for k in k_values:
                influence = results[k]
                growth_rate = (influence / base_influence - 1) * 100
                f.write(f"{k}\t{influence:.2f}\t\t{growth_rate:.1f}%\n")

            # 添加统计信息
            influences = list(results.values())
            max_influence = max(influences)
            min_influence = min(influences)
            avg_influence = sum(influences) / len(influences)

            f.write(f"\n统计信息:\n")
            f.write(f"最大影响力: {max_influence:.2f}\n")
            f.write(f"最小影响力: {min_influence:.2f}\n")
            f.write(f"平均影响力: {avg_influence:.2f}\n")
            f.write(f"总体增长率: {(max_influence / min_influence - 1) * 100:.1f}%\n")

        print(f"详细结果已保存到: {filename}")


def main():
    """
    主函数：演示GLC-IC评估的完整流程
    """
    print("GLC算法 IC模型 k=1到50 影响力传播评估")
    print("=" * 60)

    # 尝试加载网络文件
    try:
        # 创建临时评估器来加载网络
        temp_evaluator = GLCICEvaluator(nx.Graph(), network_type="karate")
        graph = temp_evaluator.glc_algorithm.load_network_from_file("networks/blog-int.txt")
        network_type = "karate"
        print(f"成功加载网络文件: karate.txt")
    except:
        try:
            # 尝试其他网络文件
            temp_evaluator = GLCICEvaluator(nx.Graph(), network_type="blog")
            graph = temp_evaluator.glc_algorithm.load_network_from_file("networks/blog-int.txt")
            network_type = "blog"
            print(f"成功加载网络文件: blog-int.txt")
        except:
            # 使用内置示例网络
            print("网络文件未找到，使用Karate Club网络...")
            graph = nx.karate_club_graph()
            network_type = "karate"

    # 创建评估器
    evaluator = GLCICEvaluator(graph, network_type=network_type)

    print(f"\n网络基本信息:")
    print(f"  网络类型: {network_type}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")
    print(f"  网络密度: {nx.density(graph):.4f}")

    # 进行k=1到50的IC模型评估
    print(f"\n开始k=1到50的IC模型评估...")
    print(f"参数设置:")
    print(f"  传播概率: 0.05")
    print(f"  模拟次数: 10000")

    # 运行评估（使用较少的模拟次数进行演示）
    results = evaluator.evaluate_k_range_ic(
        k_max=50,
        p=0.05,
        num_simulations=1000  # 演示用，实际应该是10000
    )

    # 打印结果摘要
    evaluator.print_k_range_summary()

    # 保存结果
    evaluator.save_results_to_csv()
    evaluator.save_results_to_txt()

    print(f"\n评估完成！")
    print(f"结果文件已保存，包含完整的k=1到50评估数据。")

    return evaluator


if __name__ == "__main__":
    main()
