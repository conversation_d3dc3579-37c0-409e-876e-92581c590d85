"""
GLC算法与IC（Independent Cascade）模型结合的影响力传播评估

严格按照论文要求实现的完整GLC算法，包含：
1. 聚类检测与全局关键节点选择（3.1节）
2. 节点局部影响力计算（3.2节）
3. 综合影响力计算（3.3节）
4. 蒙特卡洛IC模型评估

主要功能：
1. 完整的GLC算法实现（公式10、11、12）
2. 三度影响力规则的聚类扩展
3. 全局关键节点集合维护
4. 最短路径缓存优化
5. λ参数动态调整
6. IC模型影响力传播评估

作者：严格按照论文实现
"""

import networkx as nx
import random
import numpy as np
import time
from typing import Dict, List, Set, Tuple, Union
from collections import defaultdict


def mc_influence(G: nx.Graph, seed_arr: List[int], p: float, NUM_SIMUS: int = 1000) -> float:
    """
    蒙特卡洛模拟的IC（Independent Cascade）模型

    严格按照论文IC模型实现，确保传播机制的准确性

    Args:
        G: NetworkX图对象
        seed_arr: 种子节点列表
        p: 传播概率
        NUM_SIMUS: 模拟次数，默认1000

    Returns:
        平均影响力（激活节点数）
    """
    if not seed_arr:
        return 0.0

    # 简化输出，避免过多打印
    inf = 0
    num_nodes = G.number_of_nodes()

    for r in range(NUM_SIMUS):
        # 初始化激活节点集合
        active = set(seed_arr)
        new_active = set(seed_arr)

        # IC模型传播过程：每轮新激活的节点尝试激活其邻居
        while new_active:
            next_active = set()
            for node in new_active:
                neighbors = set(G.neighbors(node))
                for neighbor in neighbors:
                    if neighbor not in active:
                        # 以概率p激活邻居节点（Independent Cascade核心机制）
                        if random.random() < p:
                            next_active.add(neighbor)
                            active.add(neighbor)
            new_active = next_active

        inf += len(active)

    average_influence = inf / NUM_SIMUS
    return average_influence


class GLCAlgorithm:
    """
    完整的GLC算法实现

    严格按照论文要求实现所有核心组件：
    1. 聚类潜力计算（公式10）
    2. 聚类检测与全局关键节点选择（3.1节）
    3. 局部影响力计算（公式11）
    4. 综合影响力计算（公式12）
    """

    def __init__(self, graph: nx.Graph, lambda_param: float = 0.8):
        """
        初始化GLC算法

        Args:
            graph: NetworkX图对象
            lambda_param: 聚类覆盖率参数
        """
        self.graph = graph.copy()
        self.lambda_param = lambda_param

        # 算法状态
        self.k_shell_values = {}
        self.pc_values = {}
        self.clusters = []
        self.global_critical_nodes = set()
        self.local_influence = {}
        self.glc_values = {}

        # 缓存优化
        self.shortest_path_cache = defaultdict(dict)

    def compute_k_shell(self) -> Dict[int, int]:
        """
        计算所有节点的k-shell值

        Returns:
            节点到k-shell值的映射字典
        """
        print("   计算k-shell分解...")
        self.k_shell_values = nx.core_number(self.graph)
        return self.k_shell_values

    def calculate_clustering_potential(self) -> Dict[int, float]:
        """
        计算聚类潜力（公式10）

        pc_i = k_i * Σ(k_j^in) for j ∈ N(i)
        其中 k_j^in 是邻居节点j与节点i及其邻居的连接数

        Returns:
            节点到聚类潜力的映射字典
        """
        print("   计算聚类潜力（公式10）...")
        pc_values = {}

        for node in self.graph.nodes():
            k_i = self.graph.degree(node)  # 节点i的度数
            neighbors_i = set(self.graph.neighbors(node))  # N(i)

            # 计算 Σ(k_j^in) for j ∈ N(i)
            sum_k_in = 0
            for neighbor_j in neighbors_i:
                # k_j^in: 邻居节点j与"节点i及其邻居"的连接数
                neighbors_j = set(self.graph.neighbors(neighbor_j))
                i_and_its_neighbors = neighbors_i | {node}  # {i} ∪ N(i)
                k_j_in = len(neighbors_j.intersection(i_and_its_neighbors))
                sum_k_in += k_j_in

            # 公式(10): pc_i = k_i * sum(k_j^in)
            pc_values[node] = k_i * sum_k_in

        self.pc_values = pc_values
        return pc_values

    def detect_clusters(self) -> List[Set[int]]:
        """
        聚类检测算法（3.1节）

        实现论文Algorithm 1中的聚类检测过程：
        1. 选择pc值最大的节点作为初始节点
        2. 添加pc值超过pcmax/2的邻居节点
        3. 通过三度影响规则扩展聚类（重复3次）
        4. 重复直到覆盖λ比例的节点

        Returns:
            聚类列表，每个聚类是节点集合
        """
        print("   执行聚类检测算法...")

        if not self.pc_values:
            self.calculate_clustering_potential()

        clusters = []
        remaining_nodes = set(self.graph.nodes())
        pc_values_copy = self.pc_values.copy()
        total_nodes = len(self.graph.nodes())
        target_coverage = int(total_nodes * self.lambda_param)
        covered_nodes = 0

        print(f"   目标覆盖节点数: {target_coverage} (λ={self.lambda_param})")

        while covered_nodes < target_coverage and remaining_nodes:
            # Step 1: 选择pc值最大的节点作为初始节点
            if not any(pc_values_copy[node] > 0 for node in remaining_nodes):
                break

            # 找到pc值最大的节点
            max_pc = max(pc_values_copy[node] for node in remaining_nodes if pc_values_copy[node] > 0)
            candidates = [node for node in remaining_nodes if pc_values_copy[node] == max_pc]

            # 在候选节点中选择度数最大的作为初始节点
            initial_node = max(candidates, key=lambda x: self.graph.degree(x))

            # 创建新聚类
            cluster = {initial_node}

            # 添加pc值超过pcmax/2的邻居节点
            threshold = max_pc / 2
            for neighbor in self.graph.neighbors(initial_node):
                if neighbor in remaining_nodes and pc_values_copy[neighbor] >= threshold:
                    cluster.add(neighbor)

            # Step 2: 通过三度影响规则扩展聚类（重复3次）
            for iteration in range(3):  # 三度影响力规则
                new_nodes = set()

                # 获取聚类的邻居节点
                cluster_neighbors = set()
                for node in cluster:
                    cluster_neighbors.update(self.graph.neighbors(node))
                cluster_neighbors -= cluster  # 移除已在聚类中的节点
                cluster_neighbors &= remaining_nodes  # 只考虑剩余节点

                # 按度数升序检查邻居节点
                sorted_neighbors = sorted(cluster_neighbors, key=lambda x: self.graph.degree(x))

                for neighbor in sorted_neighbors:
                    # 计算k_in和k_out
                    k_in = len(set(self.graph.neighbors(neighbor)) & cluster)
                    k_out = self.graph.degree(neighbor) - k_in

                    # 如果k_in >= k_out，则加入聚类
                    if k_in >= k_out:
                        new_nodes.add(neighbor)

                cluster.update(new_nodes)

            # Step 3: 将聚类中的节点pc值设为0
            for node in cluster:
                pc_values_copy[node] = 0

            clusters.append(cluster)
            remaining_nodes -= cluster
            covered_nodes += len(cluster)

        self.clusters = clusters
        print(f"   检测到 {len(clusters)} 个聚类，覆盖 {covered_nodes} 个节点")
        return clusters

    def select_global_critical_nodes(self) -> Set[int]:
        """
        选择全局关键节点

        从每个聚类中选择度数最大的节点作为全局关键节点

        Returns:
            全局关键节点集合
        """
        print("   选择全局关键节点...")

        if not self.clusters:
            self.detect_clusters()

        global_critical_nodes = set()

        for cluster in self.clusters:
            if cluster:  # 确保聚类非空
                # 选择聚类中度数最大的节点
                critical_node = max(cluster, key=lambda x: self.graph.degree(x))
                global_critical_nodes.add(critical_node)

        self.global_critical_nodes = global_critical_nodes
        print(f"   选择了 {len(global_critical_nodes)} 个全局关键节点")
        return global_critical_nodes

    def compute_local_influence(self) -> Dict[int, float]:
        """
        计算局部影响力（公式11）

        LI_i = NCC_i = Σ(ks_j) for j ∈ N(i)
        其中 ks_j 是邻居节点j的k-shell值

        Returns:
            节点到局部影响力的映射字典
        """
        print("   计算局部影响力（公式11）...")

        if not self.k_shell_values:
            self.compute_k_shell()

        local_influence = {}

        for node in self.graph.nodes():
            # 计算邻居节点的k-shell值之和
            li_value = sum(self.k_shell_values[neighbor]
                          for neighbor in self.graph.neighbors(node))
            local_influence[node] = li_value

        self.local_influence = local_influence
        return local_influence

    def precompute_shortest_paths(self):
        """
        预计算全局关键节点的最短路径（优化性能）
        """
        print("   预计算最短路径...")

        if not self.global_critical_nodes:
            self.select_global_critical_nodes()

        # 为每个全局关键节点计算到所有其他节点的最短路径
        for critical_node in self.global_critical_nodes:
            try:
                paths = nx.single_source_shortest_path_length(self.graph, critical_node)
                for target_node, distance in paths.items():
                    self.shortest_path_cache[critical_node][target_node] = distance
            except Exception as e:
                print(f"   警告：计算节点{critical_node}的最短路径时出错: {e}")

    def get_shortest_distance(self, source: int, target: int) -> float:
        """
        获取两个节点间的最短距离（使用缓存）

        Args:
            source: 源节点
            target: 目标节点

        Returns:
            最短路径长度，如果不可达返回无穷大
        """
        if target in self.shortest_path_cache[source]:
            return self.shortest_path_cache[source][target]

        try:
            distance = nx.shortest_path_length(self.graph, source, target)
            self.shortest_path_cache[source][target] = distance
            return distance
        except nx.NetworkXNoPath:
            return float('inf')

    def calculate_glc_centrality(self) -> Dict[int, float]:
        """
        计算GLC中心性（公式12）

        GLC_i = LI_i * Σ(LI_u / 2^{d_iu}) for u ∈ C
        = Σ(ks_j for j ∈ N(i)) * Σ(Σ(ks_m for m ∈ N(u)) / 2^{d_iu} for u ∈ C)

        Returns:
            节点到GLC中心性的映射字典
        """
        print("   计算GLC中心性（公式12）...")

        # 确保所有前置步骤已完成
        if not self.local_influence:
            self.compute_local_influence()

        if not self.global_critical_nodes:
            self.select_global_critical_nodes()

        # 预计算最短路径以提高效率
        self.precompute_shortest_paths()

        glc_values = {}

        for node in self.graph.nodes():
            # 第一部分：局部影响力 LI_i
            li_i = self.local_influence[node]

            # 第二部分：全局影响力 Σ(LI_u / 2^{d_iu}) for u ∈ C
            global_influence_sum = 0

            for critical_node in self.global_critical_nodes:
                # 获取全局关键节点的局部影响力
                li_u = self.local_influence[critical_node]

                # 获取最短路径距离
                d_iu = self.get_shortest_distance(node, critical_node)

                # 应用衰减因子 1/2^d
                if d_iu != float('inf') and d_iu > 0:
                    global_influence_sum += li_u / (2 ** d_iu)
                elif d_iu == 0:  # 节点本身就是全局关键节点
                    global_influence_sum += li_u
                # 如果不可达，贡献为0（不添加）

            # 公式(12): GLC_i = LI_i * 全局影响力
            glc_values[node] = li_i * global_influence_sum

        self.glc_values = glc_values
        return glc_values

    def run_glc_algorithm(self) -> Dict[int, float]:
        """
        运行完整的GLC算法

        按照论文Algorithm 1的步骤执行：
        1. k-shell分解
        2. 聚类潜力计算
        3. 聚类检测
        4. 全局关键节点选择
        5. 局部影响力计算
        6. GLC中心性计算

        Returns:
            节点到GLC中心性的映射字典
        """
        print("开始运行GLC算法...")

        # Step 1: k-shell分解
        self.compute_k_shell()

        # Step 2: 聚类潜力计算（公式10）
        self.calculate_clustering_potential()

        # Step 3: 聚类检测与全局关键节点选择（3.1节）
        self.detect_clusters()
        self.select_global_critical_nodes()

        # Step 4: 局部影响力计算（公式11）
        self.compute_local_influence()

        # Step 5: GLC中心性计算（公式12）
        self.calculate_glc_centrality()

        print(f"GLC算法完成！计算了 {len(self.glc_values)} 个节点的GLC值")
        return self.glc_values

    def load_network_from_file(self, filename: str) -> nx.Graph:
        """
        从文件加载网络

        Args:
            filename: 网络文件路径

        Returns:
            NetworkX图对象
        """
        try:
            # 尝试不同的文件格式
            if filename.endswith('.gml'):
                graph = nx.read_gml(filename)
            elif filename.endswith('.graphml'):
                graph = nx.read_graphml(filename)
            else:
                # 假设是边列表格式
                graph = nx.read_edgelist(filename, nodetype=int)

            # 确保图是无向的
            if graph.is_directed():
                graph = graph.to_undirected()

            # 移除自环
            graph.remove_edges_from(nx.selfloop_edges(graph))

            # 只保留最大连通分量
            if not nx.is_connected(graph):
                largest_cc = max(nx.connected_components(graph), key=len)
                graph = graph.subgraph(largest_cc).copy()

            # 重新映射节点ID为连续整数
            mapping = {node: i for i, node in enumerate(graph.nodes())}
            graph = nx.relabel_nodes(graph, mapping)

            self.graph = graph
            return graph

        except Exception as e:
            print(f"加载网络文件失败: {e}")
            raise


class GLCInfluenceEvaluator:
    """
    GLC算法影响力传播评估器

    根据论文要求修正的版本，确保公式实现的准确性
    """

    def __init__(self, graph: nx.Graph, k: int = 50, network_type: str = "unknown"):
        """
        初始化评估器

        Args:
            graph: NetworkX图对象
            k: 选择的种子节点数量，默认50
            network_type: 网络类型，用于设置λ参数
        """
        self.graph = graph.copy()
        self.k = k
        self.network_type = network_type

        # 根据论文Section 6实验结果设置各网络的最优λ*参数
        # 这些值是通过Kendall tau相关系数优化得出的
        self.lambda_mapping = {
            'netscience': 1.0,    # 网络结构紧密，需覆盖全部节点以捕捉全局关键节点
            'facebook': 0.95,     # 高度聚集的社交网络，接近全覆盖但保留少量节点避免噪声
            'infectious': 0.4,    # 中等规模网络，部分覆盖即可平衡局部和全局信息
            'yeast': 0.7,         # 存在明显社区结构，需覆盖较多节点以连接不同社区
            'protein': 0.05,      # 稀疏连接，过早扩大簇会引入无关节点，降低关键节点识别精度
            'ca-grqc': 0.4,       # 类似Infectious，中等覆盖率能有效识别跨社区的关键节点
            'blog': 0.7,          # 博客网络，中等社区结构
            'karate': 0.5,        # 小规模网络，中等覆盖率
            'powergrid': 0.6,     # 电力网络，需要较高覆盖率
            'unknown': 0.5        # 默认值：从中等覆盖率开始
        }

        # 设置λ参数（λ*）
        lambda_param = self.lambda_mapping.get(network_type.lower(), 0.5)

        # 输出λ参数选择的原因
        self._explain_lambda_choice(network_type.lower(), lambda_param)

        # 初始化GLC算法，使用我们完整实现的GLCAlgorithm
        self.glc_algorithm = GLCAlgorithm(graph, lambda_param=lambda_param)
        self.centrality_methods = {}
        self.influence_results = {}
        self.k_range_results = {}  # 存储k=1到50的结果

        # 预计算缓存，提高效率
        self.shortest_path_cache = {}
        self.glc_computed = False

    def _explain_lambda_choice(self, network_type: str, lambda_param: float):
        """
        解释λ参数选择的原因

        Args:
            network_type: 网络类型
            lambda_param: 选择的λ值
        """
        explanations = {
            'netscience': "科学合作网络：结构紧密，需要全覆盖(λ=1.0)以捕捉所有关键节点",
            'facebook': "社交网络：高度聚集，接近全覆盖(λ=0.95)但避免噪声节点",
            'infectious': "传染病网络：中等规模，部分覆盖(λ=0.4)平衡局部和全局信息",
            'yeast': "酵母蛋白网络：明显社区结构，较高覆盖率(λ=0.7)连接不同社区",
            'protein': "蛋白质网络：稀疏连接，低覆盖率(λ=0.05)避免引入无关节点",
            'ca-grqc': "引文网络：中等覆盖率(λ=0.4)识别跨社区关键节点",
            'blog': "博客网络：中等社区结构，适中覆盖率(λ=0.7)",
            'karate': "空手道俱乐部：小规模网络，中等覆盖率(λ=0.5)",
            'powergrid': "电力网络：基础设施网络，较高覆盖率(λ=0.6)",
            'unknown': "未知网络类型：使用默认中等覆盖率(λ=0.5)，建议进行λ优化"
        }

        explanation = explanations.get(network_type, explanations['unknown'])
        print(f"   λ参数选择: {lambda_param} - {explanation}")

        if network_type == 'unknown':
            print("   建议：对于新网络，可使用optimize_lambda()方法寻找最优λ值")

    def optimize_lambda(self, lambda_range: List[float] = None,
                       evaluation_method: str = "kendall_tau") -> float:
        """
        优化λ参数

        根据论文方法，通过网格搜索找到最优λ值

        Args:
            lambda_range: λ值搜索范围，默认[0.1, 0.2, ..., 1.0]
            evaluation_method: 评估方法，默认使用kendall_tau

        Returns:
            最优λ值
        """
        if lambda_range is None:
            lambda_range = [i * 0.1 for i in range(1, 11)]  # [0.1, 0.2, ..., 1.0]

        print(f"\n开始λ参数优化...")
        print(f"搜索范围: {lambda_range}")
        print(f"评估方法: {evaluation_method}")
        print("=" * 60)

        best_lambda = 0.5
        best_score = -1
        results = {}

        original_lambda = self.glc_algorithm.lambda_param

        for lambda_val in lambda_range:
            print(f"\n测试λ = {lambda_val}...")

            # 重新初始化GLC算法
            self.glc_algorithm = GLCAlgorithm(self.graph, lambda_param=lambda_val)

            # 运行GLC算法
            self.glc_algorithm.run_glc_algorithm()

            # 评估性能
            if evaluation_method == "kendall_tau":
                score = self._evaluate_kendall_tau()
            elif evaluation_method == "ic_correlation":
                score = self._evaluate_ic_correlation()
            else:
                score = self._evaluate_clustering_quality()

            results[lambda_val] = score
            print(f"   评估得分: {score:.4f}")

            if score > best_score:
                best_score = score
                best_lambda = lambda_val

        print(f"\n优化完成！")
        print(f"最优λ值: {best_lambda} (得分: {best_score:.4f})")

        # 使用最优λ重新初始化
        self.glc_algorithm = GLCAlgorithm(self.graph, lambda_param=best_lambda)
        self.glc_computed = False

        return best_lambda

    def _evaluate_kendall_tau(self) -> float:
        """
        使用Kendall tau相关系数评估GLC排名质量

        Returns:
            Kendall tau系数
        """
        try:
            # 计算度中心性作为基准
            degree_centrality = nx.degree_centrality(self.graph)
            degree_ranking = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)

            # 获取GLC排名
            glc_ranking = sorted(self.glc_algorithm.glc_values.items(), key=lambda x: x[1], reverse=True)

            # 计算Kendall tau
            from scipy.stats import kendalltau

            # 提取节点排序
            degree_order = [node for node, _ in degree_ranking]
            glc_order = [node for node, _ in glc_ranking]

            # 创建排名映射
            degree_ranks = {node: i for i, node in enumerate(degree_order)}
            glc_ranks = {node: i for i, node in enumerate(glc_order)}

            # 计算相关系数
            common_nodes = set(degree_ranks.keys()) & set(glc_ranks.keys())
            degree_rank_values = [degree_ranks[node] for node in common_nodes]
            glc_rank_values = [glc_ranks[node] for node in common_nodes]

            tau, _ = kendalltau(degree_rank_values, glc_rank_values)
            return abs(tau)  # 返回绝对值

        except Exception as e:
            print(f"   警告：Kendall tau计算失败: {e}")
            return 0.0

    def _evaluate_ic_correlation(self) -> float:
        """
        使用IC模型相关性评估GLC性能

        Returns:
            IC相关性得分
        """
        try:
            # 选择前20个GLC节点进行IC模拟
            glc_ranking = sorted(self.glc_algorithm.glc_values.items(), key=lambda x: x[1], reverse=True)
            top_nodes = [node for node, _ in glc_ranking[:20]]

            # 计算IC影响力
            ic_influence = mc_influence(self.graph, top_nodes, p=0.1, NUM_SIMUS=100)

            # 归一化得分（简化评估）
            max_possible = len(self.graph.nodes())
            score = ic_influence / max_possible

            return score

        except Exception as e:
            print(f"   警告：IC相关性计算失败: {e}")
            return 0.0

    def _evaluate_clustering_quality(self) -> float:
        """
        评估聚类质量

        Returns:
            聚类质量得分
        """
        try:
            if not self.glc_algorithm.clusters:
                return 0.0

            # 计算模块度作为聚类质量指标
            total_score = 0
            total_clusters = len(self.glc_algorithm.clusters)

            for cluster in self.glc_algorithm.clusters:
                if len(cluster) > 1:
                    subgraph = self.graph.subgraph(cluster)
                    # 计算聚类内部连接密度
                    internal_edges = subgraph.number_of_edges()
                    possible_edges = len(cluster) * (len(cluster) - 1) / 2
                    if possible_edges > 0:
                        density = internal_edges / possible_edges
                        total_score += density

            return total_score / total_clusters if total_clusters > 0 else 0.0

        except Exception as e:
            print(f"   警告：聚类质量计算失败: {e}")
            return 0.0

    def compute_all_centralities(self):
        """
        计算所有中心性指标

        确保GLC算法按照论文公式正确实现
        """
        print("计算各种中心性指标...")

        # 1. GLC中心性 - 严格按照论文公式实现
        print("1. 计算GLC中心性...")
        print(f"   使用λ参数: {self.glc_algorithm.lambda_param}")

        # 运行修正后的GLC算法
        self.glc_algorithm.run_glc_algorithm()
        glc_values = self.glc_algorithm.glc_values

        # 验证GLC值的合理性
        if not glc_values:
            raise ValueError("GLC算法计算失败，未获得有效的GLC值")

        # 按GLC值降序排序，选择前k个节点
        glc_top_k = sorted(glc_values.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['GLC'] = [node for node, _ in glc_top_k]
        self.glc_computed = True

        print(f"   GLC前10节点: {[node for node, _ in glc_top_k[:10]]}")
        print(f"   GLC值范围: {glc_top_k[0][1]:.4f} ~ {glc_top_k[-1][1]:.4f}")

        # 2. 度中心性
        print("2. 计算度中心性...")
        degree_centrality = nx.degree_centrality(self.graph)
        degree_top_k = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Degree'] = [node for node, _ in degree_top_k]

        # 3. 介数中心性
        print("3. 计算介数中心性...")
        betweenness_centrality = nx.betweenness_centrality(self.graph)
        betweenness_top_k = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Betweenness'] = [node for node, _ in betweenness_top_k]

        # 4. 接近中心性
        print("4. 计算接近中心性...")
        closeness_centrality = nx.closeness_centrality(self.graph)
        closeness_top_k = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['Closeness'] = [node for node, _ in closeness_top_k]

        # 5. 特征向量中心性
        print("5. 计算特征向量中心性...")
        try:
            eigenvector_centrality = nx.eigenvector_centrality(self.graph, max_iter=1000)
            eigenvector_top_k = sorted(eigenvector_centrality.items(), key=lambda x: x[1], reverse=True)[:self.k]
            self.centrality_methods['Eigenvector'] = [node for node, _ in eigenvector_top_k]
        except:
            print("   特征向量中心性计算失败，跳过...")

        # 6. PageRank
        print("6. 计算PageRank...")
        pagerank = nx.pagerank(self.graph)
        pagerank_top_k = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['PageRank'] = [node for node, _ in pagerank_top_k]

        # 7. k-shell（k-core）
        print("7. 计算k-shell中心性...")
        k_shell = nx.core_number(self.graph)
        k_shell_top_k = sorted(k_shell.items(), key=lambda x: x[1], reverse=True)[:self.k]
        self.centrality_methods['K-Shell'] = [node for node, _ in k_shell_top_k]

        print(f"完成所有中心性指标计算，共{len(self.centrality_methods)}种方法")

    def evaluate_influence_spread(self, p_values: List[float] = [0.01, 0.05, 0.1, 0.2],
                                 num_simulations: int = 1000):
        """
        评估不同传播概率下的影响力传播效果

        Args:
            p_values: 传播概率列表
            num_simulations: 每次评估的模拟次数
        """
        if not self.centrality_methods:
            self.compute_all_centralities()

        print(f"\n开始影响力传播评估...")
        print(f"种子节点数: {self.k}")
        print(f"传播概率: {p_values}")
        print(f"模拟次数: {num_simulations}")
        print("=" * 80)

        # 初始化结果存储
        for method in self.centrality_methods:
            self.influence_results[method] = {}

        # 对每个传播概率进行评估
        for p in p_values:
            print(f"\n传播概率 p = {p}")
            print("-" * 50)

            for method_name, seed_nodes in self.centrality_methods.items():
                print(f"\n评估方法: {method_name}")
                start_time = time.time()

                # 计算影响力
                influence = mc_influence(self.graph, seed_nodes, p, num_simulations)

                end_time = time.time()
                print(f"计算时间: {end_time - start_time:.2f} 秒")

                # 存储结果
                self.influence_results[method_name][p] = influence

        print("\n影响力传播评估完成！")

    def evaluate_glc_k_range(self, k_max: int = 50, p_values: List[float] = [0.05, 0.1, 0.2],
                            num_simulations: int = 1000):
        """
        评估GLC算法在k=1到k_max范围内的影响力传播效果

        严格按照中心性算法要求：k=20包含k=10的所有节点

        Args:
            k_max: 最大种子节点数，默认50
            p_values: 传播概率列表
            num_simulations: 每次评估的模拟次数
        """
        # 确保GLC算法已运行且使用正确的公式
        if not self.glc_computed or not self.glc_algorithm.glc_values:
            print("运行修正后的GLC算法...")
            self.glc_algorithm.run_glc_algorithm()
            self.glc_computed = True

        # 获取按GLC值排序的节点列表（单一排序保证包含性）
        glc_sorted_nodes = sorted(self.glc_algorithm.glc_values.items(),
                                 key=lambda x: x[1], reverse=True)

        print(f"\n开始评估GLC算法k=1到{k_max}的影响力传播...")
        print(f"网络类型: {self.network_type}")
        print(f"λ参数: {self.glc_algorithm.lambda_param}")
        print(f"传播概率: {p_values}")
        print(f"模拟次数: {num_simulations}")
        print(f"GLC聚类数: {len(self.glc_algorithm.clusters) if self.glc_algorithm.clusters else 0}")
        print(f"全局关键节点数: {len(self.glc_algorithm.global_critical_nodes) if self.glc_algorithm.global_critical_nodes else 0}")
        print("=" * 80)

        # 验证节点排序的合理性
        if len(glc_sorted_nodes) < k_max:
            print(f"警告：网络节点数({len(glc_sorted_nodes)})小于k_max({k_max})")
            k_max = min(k_max, len(glc_sorted_nodes))

        # 初始化结果存储
        for p in p_values:
            self.k_range_results[p] = {}

        # 对每个k值进行评估（确保包含性：k=20包含k=10的所有节点）
        for k in range(1, k_max + 1):
            if k % 10 == 0 or k <= 5:
                print(f"\n评估k={k}...")

            # 获取前k个GLC节点（从同一排序列表确保包含性）
            top_k_nodes = [node for node, _ in glc_sorted_nodes[:k]]

            # 验证包含性（调试用）
            if k > 1:
                prev_nodes = set([node for node, _ in glc_sorted_nodes[:k-1]])
                curr_nodes = set(top_k_nodes)
                if not prev_nodes.issubset(curr_nodes):
                    raise ValueError(f"k={k}的节点集合不包含k={k-1}的节点集合，违反中心性算法要求")

            # 对每个传播概率进行测试
            for p in p_values:
                # 计算影响力
                influence = mc_influence(self.graph, top_k_nodes, p, num_simulations)

                # 存储结果
                self.k_range_results[p][k] = influence

                if k % 10 == 0 or k <= 5:
                    print(f"  p={p}: {influence:.2f}")

        print(f"\nGLC算法k=1到{k_max}评估完成！")

        # 验证结果的单调性（影响力应该随k增加而增加或保持不变）
        self._validate_monotonicity()

        return self.k_range_results

    def _validate_monotonicity(self):
        """验证影响力的单调性"""
        for p in self.k_range_results:
            k_values = sorted(self.k_range_results[p].keys())
            for i in range(1, len(k_values)):
                curr_k = k_values[i]
                prev_k = k_values[i-1]
                curr_inf = self.k_range_results[p][curr_k]
                prev_inf = self.k_range_results[p][prev_k]

                if curr_inf < prev_inf - 0.01:  # 允许小的数值误差
                    print(f"警告：p={p}时，k={curr_k}的影响力({curr_inf:.2f})小于k={prev_k}的影响力({prev_inf:.2f})")
                    print("这可能表明算法实现存在问题或随机性影响")

    def print_comparison_results(self):
        """打印比较结果"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        print(f"\n{'='*80}")
        print("影响力传播比较结果")
        print(f"{'='*80}")

        # 获取所有传播概率
        p_values = list(next(iter(self.influence_results.values())).keys())

        # 打印表头
        header = "方法名称\t\t"
        for p in p_values:
            header += f"p={p}\t\t"
        print(header)
        print("-" * 80)

        # 打印每种方法的结果
        for method_name in self.influence_results:
            row = f"{method_name:<15}\t"
            for p in p_values:
                influence = self.influence_results[method_name][p]
                row += f"{influence:.2f}\t\t"
            print(row)

        # 找出每个概率下的最佳方法
        print(f"\n{'='*50}")
        print("最佳方法总结")
        print(f"{'='*50}")

        for p in p_values:
            best_method = max(self.influence_results.keys(),
                            key=lambda x: self.influence_results[x][p])
            best_influence = self.influence_results[best_method][p]
            print(f"p={p}: {best_method} (影响力: {best_influence:.2f})")

    def analyze_glc_performance(self):
        """分析GLC算法的性能表现"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        print(f"\n{'='*60}")
        print("GLC算法性能分析")
        print(f"{'='*60}")

        p_values = list(self.influence_results['GLC'].keys())

        for p in p_values:
            print(f"\n传播概率 p = {p}:")
            glc_influence = self.influence_results['GLC'][p]

            # 计算相对于其他方法的性能
            better_count = 0
            total_methods = len(self.influence_results) - 1  # 除了GLC本身

            for method_name, results in self.influence_results.items():
                if method_name != 'GLC':
                    other_influence = results[p]
                    improvement = (glc_influence - other_influence) / other_influence * 100
                    if glc_influence > other_influence:
                        better_count += 1
                        print(f"  vs {method_name}: +{improvement:.1f}% (GLC更好)")
                    else:
                        print(f"  vs {method_name}: {improvement:.1f}% (GLC较差)")

            print(f"  GLC胜出率: {better_count}/{total_methods} ({better_count/total_methods:.1%})")

    def save_results_to_file(self, filename: str = "glc_ic_results.txt"):
        """保存结果到文件"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法IC模型影响力传播评估结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"种子节点数: {self.k}\n\n")

            # 写入详细结果
            p_values = list(next(iter(self.influence_results.values())).keys())

            f.write("详细结果:\n")
            f.write("方法名称\t\t")
            for p in p_values:
                f.write(f"p={p}\t\t")
            f.write("\n")
            f.write("-" * 80 + "\n")

            for method_name in self.influence_results:
                f.write(f"{method_name:<15}\t")
                for p in p_values:
                    influence = self.influence_results[method_name][p]
                    f.write(f"{influence:.2f}\t\t")
                f.write("\n")

            # 写入最佳方法总结
            f.write(f"\n最佳方法总结:\n")
            for p in p_values:
                best_method = max(self.influence_results.keys(),
                                key=lambda x: self.influence_results[x][p])
                best_influence = self.influence_results[best_method][p]
                f.write(f"p={p}: {best_method} (影响力: {best_influence:.2f})\n")

        print(f"结果已保存到文件: {filename}")

    def export_to_csv(self, filename: str = "glc_ic_results.csv"):
        """导出结果为CSV格式"""
        if not self.influence_results:
            print("请先运行 evaluate_influence_spread() 方法")
            return

        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 获取传播概率列表
            p_values = list(next(iter(self.influence_results.values())).keys())

            # 写入表头
            header = ['方法名称'] + [f'p={p}' for p in p_values]
            writer.writerow(header)

            # 写入数据
            for method_name in self.influence_results:
                row = [method_name]
                for p in p_values:
                    influence = self.influence_results[method_name][p]
                    row.append(f"{influence:.2f}")
                writer.writerow(row)

        print(f"结果已导出为CSV文件: {filename}")

    def save_k_range_results(self, filename: str = "glc_k_range_results.txt"):
        """
        保存k=1到50范围的结果到文件

        Args:
            filename: 输出文件名
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法k=1到50影响力传播结果\n")
            f.write("=" * 60 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n\n")

            # 获取传播概率列表
            p_values = list(self.k_range_results.keys())

            # 写入表头
            f.write("k值\t")
            for p in p_values:
                f.write(f"p={p}\t\t")
            f.write("\n")
            f.write("-" * 60 + "\n")

            # 写入数据
            k_max = max(max(self.k_range_results[p].keys()) for p in p_values)
            for k in range(1, k_max + 1):
                f.write(f"{k}\t")
                for p in p_values:
                    if k in self.k_range_results[p]:
                        influence = self.k_range_results[p][k]
                        f.write(f"{influence:.2f}\t\t")
                    else:
                        f.write("N/A\t\t")
                f.write("\n")

        print(f"k范围结果已保存到文件: {filename}")

    def export_k_range_to_csv(self, filename: str = "glc_k_range_results.csv"):
        """
        将k范围结果导出为CSV格式

        Args:
            filename: CSV文件名
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 获取传播概率列表
            p_values = list(self.k_range_results.keys())

            # 写入表头
            header = ['k'] + [f'p={p}' for p in p_values]
            writer.writerow(header)

            # 写入数据
            k_max = max(max(self.k_range_results[p].keys()) for p in p_values)
            for k in range(1, k_max + 1):
                row = [k]
                for p in p_values:
                    if k in self.k_range_results[p]:
                        influence = self.k_range_results[p][k]
                        row.append(f"{influence:.2f}")
                    else:
                        row.append("N/A")
                writer.writerow(row)

        print(f"k范围结果已导出为CSV文件: {filename}")

    def print_k_range_summary(self, k_values: List[int] = [1, 5, 10, 20, 30, 40, 50]):
        """
        打印k范围结果的摘要

        Args:
            k_values: 要显示的k值列表
        """
        if not self.k_range_results:
            print("请先运行 evaluate_glc_k_range() 方法")
            return

        print(f"\n{'='*60}")
        print("GLC算法k范围影响力传播摘要")
        print(f"{'='*60}")

        # 获取传播概率列表
        p_values = list(self.k_range_results.keys())

        # 打印表头
        header = "k值\t"
        for p in p_values:
            header += f"p={p}\t\t"
        print(header)
        print("-" * 60)

        # 打印指定k值的结果
        for k in k_values:
            if all(k in self.k_range_results[p] for p in p_values):
                row = f"{k}\t"
                for p in p_values:
                    influence = self.k_range_results[p][k]
                    row += f"{influence:.2f}\t\t"
                print(row)

        # 打印增长率分析
        print(f"\n增长率分析 (相对于k=1):")
        print("k值\t", end="")
        for p in p_values:
            print(f"p={p}增长率\t", end="")
        print()
        print("-" * 60)

        for k in [5, 10, 20, 30, 40, 50]:
            if all(k in self.k_range_results[p] and 1 in self.k_range_results[p] for p in p_values):
                print(f"{k}\t", end="")
                for p in p_values:
                    base_influence = self.k_range_results[p][1]
                    current_influence = self.k_range_results[p][k]
                    if base_influence > 0:
                        growth_rate = (current_influence / base_influence - 1) * 100
                        print(f"+{growth_rate:.1f}%\t\t", end="")
                    else:
                        print("N/A\t\t", end="")
                print()


def test_different_networks():
    """测试不同网络上的GLC-IC性能"""

    networks = [
        # ("networks/karate.txt", "Karate Club", 10),
        ("networks/blog-int.txt", "Blog Network", 50),
        ("networks/AS733.txt", "Power Grid", 50),
    ]

    results_summary = []

    for network_file, network_name, k_value in networks:
        try:
            print(f"\n{'='*80}")
            print(f"测试网络: {network_name}")
            print(f"文件: {network_file}")
            print(f"种子节点数: {k_value}")
            print('='*80)

            # 创建评估器
            evaluator = GLCInfluenceEvaluator(nx.Graph(), k=k_value)

            # 加载网络
            graph = evaluator.glc_algorithm.load_network_from_file(network_file)
            evaluator.graph = graph
            evaluator.glc_algorithm.graph = graph

            # 调整k值以适应网络大小
            max_k = min(k_value, len(graph.nodes()) // 3)
            evaluator.k = max_k

            print(f"网络规模: {len(graph.nodes())} 节点, {len(graph.edges())} 边")
            print(f"实际种子节点数: {max_k}")

            # 计算中心性指标
            evaluator.compute_all_centralities()

            # 评估影响力传播（使用较少的模拟次数）
            evaluator.evaluate_influence_spread(
                p_values=[0.05],
                num_simulations=200
            )

            # 分析GLC性能
            evaluator.analyze_glc_performance()

            # 保存结果
            filename = f"{network_name.lower().replace(' ', '_')}_glc_ic_results.txt"
            evaluator.save_results_to_file(filename)

            # 收集总结信息
            p_values = list(evaluator.influence_results['GLC'].keys())
            glc_performance = {}
            for p in p_values:
                glc_influence = evaluator.influence_results['GLC'][p]
                glc_performance[p] = glc_influence

            results_summary.append({
                'network': network_name,
                'nodes': len(graph.nodes()),
                'edges': len(graph.edges()),
                'k': max_k,
                'glc_performance': glc_performance
            })

        except FileNotFoundError:
            print(f"文件 {network_file} 不存在，跳过...")
        except Exception as e:
            print(f"处理 {network_name} 时出错: {e}")

    # 打印总结
    print(f"\n{'='*80}")
    print("所有网络GLC-IC测试总结")
    print('='*80)

    for result in results_summary:
        print(f"\n网络: {result['network']}")
        print(f"规模: {result['nodes']} 节点, {result['edges']} 边")
        print(f"种子数: {result['k']}")
        print("GLC影响力表现:")
        for p, influence in result['glc_performance'].items():
            coverage = influence / result['nodes'] * 100
            print(f"  p={p}: {influence:.1f} 节点 ({coverage:.1f}% 覆盖率)")


def main():
    """
    主函数，演示修正后的GLC-IC评估

    严格按照论文要求实现，确保公式正确性
    """

    # 加载网络数据
    print("加载网络数据...")
    print("=" * 60)

    # 使用正确的网络类型参数初始化评估器
    evaluator = GLCInfluenceEvaluator(nx.Graph(), k=50, network_type="blog")

    # 使用blog网络进行演示
    try:
        graph = evaluator.glc_algorithm.load_network_from_file("networks/blog-int.txt")
        evaluator.graph = graph
        evaluator.glc_algorithm.graph = graph
    except FileNotFoundError:
        print("blog-int.txt文件未找到，尝试其他网络文件...")
        # 尝试其他可能的网络文件
        alternative_files = ["networks/karate.txt", "networks/AS733.txt"]
        for alt_file in alternative_files:
            try:
                graph = evaluator.glc_algorithm.load_network_from_file(alt_file)
                evaluator.graph = graph
                evaluator.glc_algorithm.graph = graph
                # 根据文件名调整网络类型
                if "karate" in alt_file:
                    evaluator.network_type = "karate"
                    evaluator.glc_algorithm.lambda_param = evaluator.lambda_mapping["karate"]
                elif "AS733" in alt_file:
                    evaluator.network_type = "powergrid"
                    evaluator.glc_algorithm.lambda_param = evaluator.lambda_mapping["powergrid"]
                print(f"使用替代网络文件: {alt_file}")
                break
            except FileNotFoundError:
                continue
        else:
            raise FileNotFoundError("未找到可用的网络文件")

    # 固定k=50，严格按照论文要求
    evaluator.k = 50

    print(f"网络加载完成：")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  网络密度: {nx.density(graph):.4f}")
    print(f"  网络类型: {evaluator.network_type}")
    print(f"  λ参数: {evaluator.glc_algorithm.lambda_param}")
    print(f"  种子节点数固定为: {evaluator.k}")
    print("=" * 60)

    # 评估GLC算法k=1到50的影响力传播（使用修正后的算法）
    print("\n开始k=1到50范围的影响力传播评估...")
    evaluator.evaluate_glc_k_range(
        k_max=50,
        p_values=[0.05, 0.1, 0.2],
        num_simulations=500
    )

    # 打印k范围结果摘要
    evaluator.print_k_range_summary()

    # 保存k范围结果
    network_prefix = evaluator.network_type.lower()
    evaluator.save_k_range_results(f"{network_prefix}_glc_k_range_results.txt")
    evaluator.export_k_range_to_csv(f"{network_prefix}_glc_k_range_results.csv")

    print(f"\n评估完成！结果已保存为 {network_prefix}_glc_k_range_results.*")

    return evaluator


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行多网络测试
        test_different_networks()
    else:
        # 运行单个网络示例
        main()