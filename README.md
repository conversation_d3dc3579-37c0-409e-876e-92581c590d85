# GLC (Global-Local Centrality) 算法实现

## 简介

本项目实现了GLC（Global-Local Centrality）算法，这是一种结合局部影响力和全局影响力来识别网络中关键节点的中心性算法。

## 算法原理

GLC算法包含三个主要步骤：

1. **群组检测和全局关键节点选择**
   - 计算节点的聚类潜力（pc值）
   - 使用聚类扩展算法检测网络中的群组
   - 从每个群组中选择度数最大的节点作为全局关键节点

2. **局部影响力计算**
   - 基于k-shell分解计算节点的局部影响力
   - 局部影响力 = 邻居节点k-shell值的总和

3. **整体影响力计算**
   - 结合局部影响力和到全局关键节点的距离
   - GLC值 = 局部影响力 × Σ(全局关键节点影响力 / 2^距离)

## 文件结构

```
GLC/
├── GLC.py                    # 主算法实现
├── GLC.txt                   # 算法详细说明文档
├── README.md                 # 使用说明
├── networks/                 # 网络数据文件夹
│   ├── karate.txt           # Karate Club网络
│   ├── power.txt            # 电力网格网络
│   ├── ba.txt               # Barabási-Albert网络
│   ├── er.txt               # Erdős-Rényi网络
│   └── ws.txt               # Watts-Strogatz网络
└── 结果文件/
    ├── karate_glc_results.txt
    └── karate_glc_results.csv
```

## 使用方法

### 基本使用

```python
from GLC import GLCCentrality
import networkx as nx

# 创建GLC算法实例
glc = GLCCentrality(nx.Graph())

# 加载网络数据
graph = glc.load_network_from_file("networks/karate.txt")
glc.graph = graph

# 运行GLC算法
glc.run_glc_algorithm()

# 打印结果
glc.print_results(top_k=10)
```

### 命令行使用

```bash
# 运行单个网络示例（Karate Club）
python GLC.py

# 测试多个网络
python GLC.py test
```

### 主要功能

1. **算法执行**
   - `run_glc_algorithm()`: 运行完整的GLC算法
   - `get_top_k_nodes(k)`: 获取前k个最重要的节点

2. **结果分析**
   - `print_results(top_k)`: 打印详细结果
   - `analyze_network_properties()`: 分析网络基本属性
   - `compare_with_other_centralities(top_k)`: 与其他中心性指标比较

3. **结果导出**
   - `save_results_to_file(filename)`: 保存结果到文本文件
   - `export_to_csv(filename)`: 导出为CSV格式

## 网络数据格式

网络数据文件格式：
```
节点数 边数
节点1 节点2
节点1 节点3
...
```

示例（karate.txt）：
```
34 78
1 0
2 0
2 1
...
```

## 算法参数

- `lambda_param`: 聚类覆盖比例参数（默认0.8）
  - 控制聚类检测过程中覆盖的节点比例
  - 值越大，检测到的聚类越多

## 输出结果

### 控制台输出
- 网络基本信息（节点数、边数、聚类数等）
- 聚类详细信息
- 全局关键节点列表
- 前k个最重要节点的排名
- 网络属性分析
- 与其他中心性指标的比较

### 文件输出
- **TXT文件**: 包含所有节点的详细信息
- **CSV文件**: 便于进一步分析的结构化数据

## 依赖库

```python
import networkx as nx
import numpy as np
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple, Union
import heapq
import csv
```

## 安装依赖

```bash
pip install networkx numpy
```

## 示例结果

对于Karate Club网络：
- 节点数: 34, 边数: 78
- 检测到5个聚类
- 5个全局关键节点: [0, 2, 5, 31, 33]
- 最重要的节点: 节点0 (GLC=4679.5)

## 算法特点

1. **结合局部和全局信息**: 不仅考虑节点的局部重要性，还考虑其对全局关键节点的影响能力
2. **自适应聚类检测**: 基于网络结构自动检测聚类，无需预设聚类数量
3. **高效计算**: 算法复杂度适中，适用于中等规模的网络
4. **多种输出格式**: 支持文本和CSV格式输出，便于后续分析

## 应用场景

- 社交网络影响力分析
- 生物网络关键蛋白识别
- 交通网络关键节点发现
- 信息传播网络分析
- 基础设施网络脆弱性评估
