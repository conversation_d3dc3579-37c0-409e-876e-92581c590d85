"""
GLC算法测试脚本

用于验证GLC算法实现的正确性和性能
"""

import networkx as nx
import time
from GLC import GLCCentrality


def test_small_network():
    """测试小型网络"""
    print("=== 测试小型网络 ===")
    
    # 创建一个简单的测试网络
    G = nx.Graph()
    edges = [(0, 1), (0, 2), (1, 2), (1, 3), (2, 3), (3, 4), (4, 5)]
    G.add_edges_from(edges)
    
    # 运行GLC算法
    glc = GLCCentrality(G)
    start_time = time.time()
    glc.run_glc_algorithm()
    end_time = time.time()
    
    print(f"网络规模: {len(G.nodes())} 节点, {len(G.edges())} 边")
    print(f"运行时间: {end_time - start_time:.4f} 秒")
    print(f"检测到 {len(glc.clusters)} 个聚类")
    print(f"全局关键节点: {sorted(list(glc.global_critical_nodes))}")
    
    # 显示前3个重要节点
    top_3 = glc.get_top_k_nodes(3)
    print("前3个重要节点:")
    for i, (node, glc_value) in enumerate(top_3, 1):
        print(f"  {i}. 节点{node}: GLC={glc_value:.4f}")
    
    return glc


def test_algorithm_components():
    """测试算法各个组件"""
    print("\n=== 测试算法组件 ===")
    
    # 使用karate网络
    glc = GLCCentrality(nx.Graph())
    graph = glc.load_network_from_file("networks/karate.txt")
    glc.graph = graph
    
    # 测试k-shell计算
    print("1. 测试k-shell计算...")
    k_shell = glc.compute_k_shell()
    print(f"   k-shell值范围: {min(k_shell.values())} - {max(k_shell.values())}")
    
    # 测试聚类潜力计算
    print("2. 测试聚类潜力计算...")
    pc_values = glc.compute_clustering_potential()
    max_pc_node = max(pc_values.items(), key=lambda x: x[1])
    print(f"   最大pc值: 节点{max_pc_node[0]} = {max_pc_node[1]:.2f}")
    
    # 测试聚类检测
    print("3. 测试聚类检测...")
    clusters = glc.detect_clusters()
    print(f"   检测到 {len(clusters)} 个聚类")
    for i, cluster in enumerate(clusters, 1):
        print(f"   聚类{i}: {len(cluster)} 个节点")
    
    # 测试全局关键节点选择
    print("4. 测试全局关键节点选择...")
    global_nodes = glc.select_global_critical_nodes()
    print(f"   选择了 {len(global_nodes)} 个全局关键节点: {sorted(list(global_nodes))}")
    
    # 测试局部影响力计算
    print("5. 测试局部影响力计算...")
    local_influence = glc.compute_local_influence()
    max_li_node = max(local_influence.items(), key=lambda x: x[1])
    print(f"   最大局部影响力: 节点{max_li_node[0]} = {max_li_node[1]:.2f}")
    
    # 测试GLC中心性计算
    print("6. 测试GLC中心性计算...")
    glc_values = glc.compute_glc_centrality()
    max_glc_node = max(glc_values.items(), key=lambda x: x[1])
    print(f"   最大GLC值: 节点{max_glc_node[0]} = {max_glc_node[1]:.2f}")
    
    return glc


def test_performance():
    """测试算法性能"""
    print("\n=== 性能测试 ===")
    
    networks = [
        ("networks/karate.txt", "Karate Club"),
        ("networks/power.txt", "Power Grid"),
    ]
    
    for network_file, network_name in networks:
        try:
            print(f"\n测试网络: {network_name}")
            
            # 加载网络
            glc = GLCCentrality(nx.Graph())
            graph = glc.load_network_from_file(network_file)
            glc.graph = graph
            
            n_nodes = len(graph.nodes())
            n_edges = len(graph.edges())
            
            # 测试运行时间
            start_time = time.time()
            glc.run_glc_algorithm()
            end_time = time.time()
            
            runtime = end_time - start_time
            
            print(f"  网络规模: {n_nodes} 节点, {n_edges} 边")
            print(f"  运行时间: {runtime:.4f} 秒")
            print(f"  平均每节点时间: {runtime/n_nodes*1000:.2f} 毫秒")
            print(f"  检测聚类数: {len(glc.clusters)}")
            print(f"  全局关键节点数: {len(glc.global_critical_nodes)}")
            
        except FileNotFoundError:
            print(f"  文件 {network_file} 不存在，跳过...")
        except Exception as e:
            print(f"  测试 {network_name} 时出错: {e}")


def test_parameter_sensitivity():
    """测试参数敏感性"""
    print("\n=== 参数敏感性测试 ===")
    
    # 使用karate网络测试不同的lambda参数
    lambda_values = [0.5, 0.6, 0.7, 0.8, 0.9]
    
    print("测试不同的lambda参数值:")
    print("Lambda\t聚类数\t全局节点数\t最重要节点\tGLC值")
    print("-" * 50)
    
    for lambda_val in lambda_values:
        try:
            glc = GLCCentrality(nx.Graph(), lambda_param=lambda_val)
            graph = glc.load_network_from_file("networks/karate.txt")
            glc.graph = graph
            
            glc.run_glc_algorithm()
            
            top_node = glc.get_top_k_nodes(1)[0]
            
            print(f"{lambda_val}\t{len(glc.clusters)}\t{len(glc.global_critical_nodes)}\t\t{top_node[0]}\t{top_node[1]:.2f}")
            
        except Exception as e:
            print(f"{lambda_val}\t错误: {e}")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    # 测试1: 单个节点
    print("1. 测试单个节点网络...")
    G1 = nx.Graph()
    G1.add_node(0)
    try:
        glc1 = GLCCentrality(G1)
        glc1.run_glc_algorithm()
        print("   单节点网络测试通过")
    except Exception as e:
        print(f"   单节点网络测试失败: {e}")
    
    # 测试2: 两个节点
    print("2. 测试两节点网络...")
    G2 = nx.Graph()
    G2.add_edge(0, 1)
    try:
        glc2 = GLCCentrality(G2)
        glc2.run_glc_algorithm()
        print("   两节点网络测试通过")
    except Exception as e:
        print(f"   两节点网络测试失败: {e}")
    
    # 测试3: 完全图
    print("3. 测试完全图...")
    G3 = nx.complete_graph(5)
    try:
        glc3 = GLCCentrality(G3)
        glc3.run_glc_algorithm()
        print("   完全图测试通过")
    except Exception as e:
        print(f"   完全图测试失败: {e}")
    
    # 测试4: 星形图
    print("4. 测试星形图...")
    G4 = nx.star_graph(5)
    try:
        glc4 = GLCCentrality(G4)
        glc4.run_glc_algorithm()
        print("   星形图测试通过")
        top_node = glc4.get_top_k_nodes(1)[0]
        print(f"   最重要节点: {top_node[0]} (应该是中心节点0)")
    except Exception as e:
        print(f"   星形图测试失败: {e}")


def main():
    """主测试函数"""
    print("GLC算法测试开始")
    print("=" * 60)
    
    # 运行各项测试
    test_small_network()
    test_algorithm_components()
    test_performance()
    test_parameter_sensitivity()
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")


if __name__ == "__main__":
    main()
