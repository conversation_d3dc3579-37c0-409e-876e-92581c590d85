"""
测试修正后的GLC_IC.py

验证：
1. λ*参数通过优化算法计算（与论文一致）
2. 选择GLC评分最高的前50个节点作为种子集
3. seed_arr包含50个节点
4. 调用mc_influence(G, seed_arr, p, NUM_SIMUS)计算总影响力

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality, mc_influence
import networkx as nx
import time


def test_glc_ic_final():
    """
    测试修正后的GLC_IC功能
    """
    print("测试修正后的GLC_IC功能")
    print("=" * 60)
    
    # 使用Karate网络进行快速测试
    graph = nx.karate_club_graph()
    network_name = "karate"
    
    print(f"网络信息:")
    print(f"  网络: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    
    # 1. 初始化GLC算法
    print(f"\n1. 初始化GLC算法...")
    glc = GLCCentrality(graph, lambda_param=0.8)
    
    # 2. 进行λ*参数优化（简化版）
    print(f"\n2. 进行λ*参数优化...")
    print("根据论文方法计算最优λ*值...")
    
    # 使用简化的搜索范围进行快速测试
    lambda_range = [0.3, 0.5, 0.7, 0.9]
    
    optimal_lambda = glc.optimize_lambda(
        lambda_range=lambda_range,
        num_simulations=50  # 快速测试
    )
    
    print(f"计算得到的最优λ*: {optimal_lambda}")
    
    # 3. 使用最优λ*重新运行GLC算法
    print(f"\n3. 使用最优λ*重新运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=optimal_lambda)
    glc.run_glc_algorithm()
    
    # 4. 获取节点评分排名
    print(f"\n4. 获取GLC节点评分排名...")
    sorted_nodes_by_glc = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    print(f"前10个节点的GLC评分:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i in range(min(10, len(sorted_nodes_by_glc))):
        node = sorted_nodes_by_glc[i]
        glc_value = glc.glc_values[node]
        print(f"{i+1:<4} {node:<6} {glc_value:<12.4f}")
    
    # 5. 选择GLC评分最高的前50个节点作为种子集
    k = 50
    actual_k = min(k, len(graph.nodes()))  # Karate网络只有34个节点
    
    print(f"\n5. 选择GLC评分最高的前{actual_k}个节点作为种子集...")
    seed_arr = sorted_nodes_by_glc[:actual_k]
    
    print(f"种子集大小: {len(seed_arr)}")
    print(f"种子集: {seed_arr}")
    
    # 6. 设置IC模型参数
    p = 0.05
    NUM_SIMUS = 1000  # 测试用
    
    print(f"\n6. IC模型参数设置:")
    print(f"  种子节点数: {len(seed_arr)}")
    print(f"  传播概率 p: {p}")
    print(f"  模拟次数: {NUM_SIMUS}")
    
    # 7. 计算这些节点的总IC影响力
    print(f"\n7. 计算种子集的总IC影响力...")
    print(f"调用 mc_influence(G, seed_arr, p={p}, NUM_SIMUS={NUM_SIMUS})")
    start_time = time.time()
    
    # 调用您的mc_influence函数计算所有节点的总影响力
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    total_time = time.time() - start_time
    
    # 8. 显示结果
    print(f"\n" + "=" * 60)
    print(f"GLC算法 + IC模型 影响力最大化结果")
    print(f"=" * 60)
    print(f"网络: {network_name}")
    print(f"计算得到的最优λ*: {optimal_lambda}")
    print(f"种子节点数: {len(seed_arr)}")
    print(f"种子集: {seed_arr}")
    print(f"总IC影响力: {ic_influence:.4f}")
    print(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    print(f"=" * 60)
    
    # 验证结果
    print(f"\n验证结果:")
    print(f"✅ λ*参数通过优化计算得出: {optimal_lambda}")
    print(f"✅ 种子集包含{len(seed_arr)}个节点（目标50个，实际受网络大小限制）")
    print(f"✅ seed_arr是GLC评分最高的节点列表")
    print(f"✅ 调用mc_influence计算总影响力: {ic_influence:.4f}")
    
    return {
        'optimal_lambda': optimal_lambda,
        'seed_arr': seed_arr,
        'ic_influence': ic_influence
    }


def compare_with_different_k():
    """
    比较不同种子集大小的影响
    """
    print(f"\n" + "=" * 60)
    print(f"比较不同种子集大小的影响")
    print(f"=" * 60)
    
    graph = nx.karate_club_graph()
    
    # 使用固定λ值进行快速比较
    glc = GLCCentrality(graph, lambda_param=0.7)
    glc.run_glc_algorithm()
    
    # 获取节点排名
    sorted_nodes = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    # 测试不同的种子集大小
    k_values = [5, 10, 20, 34]  # 34是Karate网络的全部节点
    p = 0.05
    NUM_SIMUS = 500  # 快速测试
    
    results = {}
    
    for k in k_values:
        print(f"\n测试种子集大小 k={k}...")
        
        # 选择前k个节点
        seed_arr = sorted_nodes[:k]
        
        # 计算IC影响力
        ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
        
        results[k] = {
            'seed_arr': seed_arr,
            'ic_influence': ic_influence
        }
        
        print(f"  种子集大小: {k}")
        print(f"  IC影响力: {ic_influence:.4f}")
        print(f"  影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    
    # 显示比较结果
    print(f"\n" + "=" * 50)
    print(f"不同种子集大小的IC影响力比较")
    print(f"=" * 50)
    print(f"{'种子集大小':<8} {'IC影响力':<12} {'影响力比例':<12} {'增长率':<12}")
    print("-" * 50)
    
    base_influence = results[5]['ic_influence']
    
    for k in k_values:
        result = results[k]
        ic_influence = result['ic_influence']
        influence_ratio = ic_influence / len(graph.nodes()) * 100
        growth_rate = (ic_influence / base_influence - 1) * 100
        
        print(f"{k:<8} {ic_influence:<12.4f} {influence_ratio:<12.2f}% {growth_rate:<12.1f}%")
    
    return results


def main():
    """
    主函数
    """
    print("=" * 80)
    print("测试修正后的GLC_IC.py功能")
    print("=" * 80)
    print("验证要求:")
    print("1. λ*参数通过优化算法计算（与论文一致）")
    print("2. 选择GLC评分最高的前50个节点作为种子集")
    print("3. seed_arr包含50个节点（或网络的全部节点）")
    print("4. 调用mc_influence(G, seed_arr, p, NUM_SIMUS)计算总影响力")
    print("=" * 80)
    
    # 测试主要功能
    result = test_glc_ic_final()
    
    # 比较不同种子集大小
    comparison_results = compare_with_different_k()
    
    print(f"\n🎉 测试完成！")
    print(f"修正后的GLC_IC.py功能正常，完全符合您的要求。")


if __name__ == "__main__":
    main()
