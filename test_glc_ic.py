"""
测试GLC_IC.py的功能
验证IC模型是否正确替换了SIR模型
"""

from GLC_IC import GLCCentrality, ICModel
import networkx as nx

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("测试GLC_IC.py基本功能")
    print("=" * 60)
    
    # 创建测试网络
    graph = nx.karate_club_graph()
    print(f"测试网络: Karate Club (节点数: {len(graph.nodes())}, 边数: {len(graph.edges())})")
    
    # 初始化GLC算法
    glc = GLCCentrality(graph, lambda_param=0.5)
    
    # 运行GLC算法
    print("\n运行GLC算法...")
    glc.run_glc_algorithm()
    
    # 获取前5个节点
    top_5 = glc.get_top_k_nodes(5)
    print("\nGLC前5个重要节点:")
    for i, (node, value) in enumerate(top_5, 1):
        print(f"  {i}. 节点{node}: GLC={value:.4f}")
    
    return glc

def test_ic_model():
    """测试IC模型"""
    print("\n" + "=" * 60)
    print("测试IC模型功能")
    print("=" * 60)
    
    # 创建简单网络
    graph = nx.path_graph(5)  # 0-1-2-3-4
    ic_model = ICModel(graph)
    
    # 测试单次IC模拟
    print("测试单次IC模拟...")
    influence = ic_model.simulate_single_ic(0, 0.5)  # 从节点0开始，传播概率0.5
    print(f"从节点0开始的影响力: {influence}")
    
    # 测试节点影响力评估
    print("\n测试节点影响力评估...")
    avg_influence = ic_model.evaluate_node_influence(0, 0.5, 100)
    print(f"节点0的平均影响力 (100次模拟): {avg_influence:.2f}")
    
    return ic_model

def test_lambda_optimization():
    """测试λ优化功能（简化版）"""
    print("\n" + "=" * 60)
    print("测试λ优化功能")
    print("=" * 60)
    
    # 使用小网络进行快速测试
    graph = nx.karate_club_graph()
    glc = GLCCentrality(graph, lambda_param=0.5)
    
    print("开始λ优化测试（简化版）...")
    try:
        optimal_lambda = glc.optimize_lambda(
            lambda_range=[0.3, 0.5, 0.7],  # 只测试3个值
            num_simulations=20  # 减少模拟次数
        )
        print(f"最优λ值: {optimal_lambda}")
        return True
    except Exception as e:
        print(f"λ优化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("GLC_IC.py 功能测试")
    print("验证IC模型是否正确替换SIR模型")
    
    try:
        # 测试基本功能
        glc = test_basic_functionality()
        
        # 测试IC模型
        ic_model = test_ic_model()
        
        # 测试λ优化
        lambda_test_passed = test_lambda_optimization()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print("✅ GLC算法基本功能: 正常")
        print("✅ IC模型功能: 正常")
        print(f"{'✅' if lambda_test_passed else '❌'} λ优化功能: {'正常' if lambda_test_passed else '异常'}")
        print("\n🎉 GLC_IC.py修正成功！IC模型已正确替换SIR模型。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
