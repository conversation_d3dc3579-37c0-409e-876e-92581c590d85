"""
测试GLC算法的影响力最大化 - k=10示例

演示影响力最大化问题：
1. GLC算法选出前10个最重要的节点作为种子集
2. 计算这10个节点共同的IC影响力
3. 展示影响力最大化的效果

参数设置：
- k=10个种子节点
- p=0.05的传播概率
- 10000次IC模拟

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import time


def test_influence_maximization_k10():
    """
    测试k=10的影响力最大化
    """
    print("GLC算法影响力最大化测试 - k=10")
    print("=" * 60)
    
    # 使用Karate网络
    graph = nx.karate_club_graph()
    network_name = "karate"
    
    print(f"网络信息:")
    print(f"  网络: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    
    # 初始化GLC算法
    glc = GLCCentrality(graph, lambda_param=0.5)
    
    # 运行GLC算法
    print(f"\n运行GLC算法...")
    glc.run_glc_algorithm()
    
    # 测试不同的k值
    k_values = [1, 5, 10, 15, 20]
    
    print(f"\n影响力最大化测试:")
    print(f"{'k值':<4} {'种子集':<30} {'IC影响力':<12} {'影响力比例':<12}")
    print("-" * 70)
    
    # 初始化IC模型
    if glc.ic_model is None:
        glc.initialize_ic_model()
    
    results = {}
    
    for k in k_values:
        if k > len(graph.nodes()):
            continue
            
        # 获取前k个节点作为种子集
        top_k_nodes = glc.get_top_k_nodes(k)
        seed_set = [node for node, _ in top_k_nodes]
        
        # 计算IC影响力
        ic_influence = glc.ic_model.mc_influence(seed_set, 0.05, 1000)  # 减少模拟次数以加快演示
        
        # 计算影响力比例
        influence_ratio = ic_influence / len(graph.nodes()) * 100
        
        # 显示种子集（如果太长则截断）
        seed_str = str(seed_set) if len(seed_set) <= 5 else f"{seed_set[:5]}..."
        
        print(f"{k:<4} {seed_str:<30} {ic_influence:<12.4f} {influence_ratio:<12.2f}%")
        
        results[k] = {
            'seed_set': seed_set,
            'ic_influence': ic_influence,
            'influence_ratio': influence_ratio
        }
    
    # 详细展示k=10的结果
    if 10 in results:
        print(f"\n" + "=" * 60)
        print(f"k=10 影响力最大化详细结果")
        print(f"=" * 60)
        
        k10_result = results[10]
        seed_set = k10_result['seed_set']
        ic_influence = k10_result['ic_influence']
        influence_ratio = k10_result['influence_ratio']
        
        print(f"种子节点数: 10")
        print(f"种子集: {seed_set}")
        print(f"总影响力: {ic_influence:.4f}")
        print(f"影响力比例: {influence_ratio:.2f}%")
        
        # 显示每个种子节点的GLC值
        top_10_nodes = glc.get_top_k_nodes(10)
        print(f"\n种子节点的GLC值:")
        print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
        print("-" * 25)
        for i, (node, glc_value) in enumerate(top_10_nodes, 1):
            print(f"{i:<4} {node:<6} {glc_value:<12.4f}")
    
    return results


def compare_with_random_selection():
    """
    与随机选择进行比较
    """
    print(f"\n" + "=" * 60)
    print(f"GLC算法 vs 随机选择比较")
    print(f"=" * 60)
    
    graph = nx.karate_club_graph()
    glc = GLCCentrality(graph, lambda_param=0.5)
    glc.run_glc_algorithm()
    
    if glc.ic_model is None:
        glc.initialize_ic_model()
    
    k = 10
    
    # GLC算法选择
    top_k_nodes = glc.get_top_k_nodes(k)
    glc_seed_set = [node for node, _ in top_k_nodes]
    glc_influence = glc.ic_model.mc_influence(glc_seed_set, 0.05, 1000)
    
    # 随机选择
    import random
    random.seed(42)  # 固定随机种子以便重现
    random_seed_set = random.sample(list(graph.nodes()), k)
    random_influence = glc.ic_model.mc_influence(random_seed_set, 0.05, 1000)
    
    print(f"k=10 影响力比较:")
    print(f"{'方法':<12} {'种子集':<30} {'IC影响力':<12} {'提升':<12}")
    print("-" * 70)
    
    improvement = (glc_influence / random_influence - 1) * 100
    
    glc_str = str(glc_seed_set) if len(glc_seed_set) <= 5 else f"{glc_seed_set[:5]}..."
    random_str = str(random_seed_set) if len(random_seed_set) <= 5 else f"{random_seed_set[:5]}..."
    
    print(f"{'GLC算法':<12} {glc_str:<30} {glc_influence:<12.4f} {'基准':<12}")
    print(f"{'随机选择':<12} {random_str:<30} {random_influence:<12.4f} {improvement:+.1f}%")
    
    print(f"\n结论: GLC算法比随机选择的影响力提升了 {improvement:.1f}%")


def main():
    """
    主函数
    """
    print("=" * 80)
    print("GLC算法影响力最大化测试")
    print("=" * 80)
    print("测试目标:")
    print("1. 验证GLC算法在影响力最大化问题中的效果")
    print("2. 比较不同k值的影响力")
    print("3. 与随机选择进行对比")
    print("=" * 80)
    
    # 测试不同k值的影响力最大化
    results = test_influence_maximization_k10()
    
    # 与随机选择比较
    compare_with_random_selection()
    
    print(f"\n🎉 测试完成！")
    print(f"GLC算法在影响力最大化问题中表现良好。")


if __name__ == "__main__":
    main()
