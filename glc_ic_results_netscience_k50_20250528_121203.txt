GLC算法 + IC模型 影响力计算结果
============================================================
网络信息:
  网络名称: netscience
  节点数: 379
  边数: 914
  平均度数: 4.82
  网络密度: 0.0128

GLC算法参数:
  λ参数: 1.0

IC模型参数:
  种子节点数 k: 50
  传播概率 p: 0.05
  模拟次数: 10000

结果:
  种子集: [19, 22, 25, 40, 56, 107, 220, 96, 100, 105, 108, 17, 1, 24, 42, 178, 21, 303, 222, 33, 71, 26, 2, 287, 166, 106, 187, 111, 148, 119, 200, 167, 169, 216, 12, 68, 104, 288, 132, 115, 110, 34, 304, 101, 20, 74, 302, 286, 289, 290]
  IC影响力: 66.6138
  影响力比例: 17.58%

GLC节点排名 (前20个):
----------------------------------------
排名   节点     GLC值        
----------------------------------------
1    <USER>     <GROUP>.9189  
2    22     64641.2754  
3    25     52859.0947  
4    40     34493.2168  
5    56     29758.3945  
6    107    28067.9414  
7    220    23756.3672  
8    96     22197.2812  
9    100    22197.2812  
10   105    22197.2812  
11   108    22197.2812  
12   17     21190.5498  
13   1      21094.1758  
14   24     20444.5635  
15   42     19808.2910  
16   178    17031.6562  
17   21     16089.7329  
18   303    16086.9160  
19   222    14973.4219  
20   33     14233.0986  

说明:
- GLC算法选出前50个最重要节点作为种子集
- 使用IC模型计算种子集的实际传播影响力
- IC影响力表示在IC模型下平均能影响多少个节点
