"""
测试GLC算法 + IC模型 k=10

完全按照您的要求和eigenvector.py的方式：
1. GLC算法给出节点评分排名（从高到低）
2. 选出前10个最高评分的节点作为种子集 seed_arr
3. 调用 mc_influence(G, seed_arr, p, NUM_SIMUS=1000) 函数
4. 计算这10个节点的IC实际传播影响力
5. 保存IC结果

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import random
import time


def mc_influence(G, seed_arr, p, NUM_SIMUS=1000):
    """
    您提供的IC模型代码
    """
    print(f"\n对种子集开始影响力计算: {seed_arr}")
    inf = 0
    
    for r in range(NUM_SIMUS):
        active = set(seed_arr)
        new_active = set(seed_arr)
        
        while new_active:
            next_active = set()
            for node in new_active:
                neighbors = set(G.neighbors(node))
                for neighbor in neighbors:
                    if neighbor not in active:
                        if random.random() < p:
                            next_active.add(neighbor)
                            active.add(neighbor)
            new_active = next_active
        inf += len(active)
    
    return inf / NUM_SIMUS


def main():
    """
    测试GLC算法 + IC模型 k=10
    """
    print("测试GLC算法 + IC模型 k=10")
    print("=" * 50)
    
    # 使用Karate网络
    graph = nx.karate_club_graph()
    network_name = "karate"
    
    print(f"网络信息:")
    print(f"  网络: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    
    # 运行GLC算法
    print(f"\n运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=0.5)
    glc.run_glc_algorithm()
    
    # 获取节点评分排名（从高到低）
    """ 对图G中的所有节点进行排序。排序依据是每个节点的GLC中心性 降序排序"""
    sorted_nodes_by_glc = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    print(f"\nGLC节点评分排名:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i in range(min(10, len(sorted_nodes_by_glc))):
        node = sorted_nodes_by_glc[i]
        glc_value = glc.glc_values[node]
        print(f"{i+1:<4} {node:<6} {glc_value:<12.4f}")
    
    # 设置参数
    k = 10
    p = 0.05
    NUM_SIMUS = 10000
    
    print(f"\n参数设置:")
    print(f"  种子节点数 k: {k}")
    print(f"  传播概率 p: {p}")
    print(f"  模拟次数: {NUM_SIMUS}")
    
    # 选择前k个节点作为种子集
    """从已经根据GLC中心性分数排序的节点列表中选择前k个节点，作为种子节点集合seed_arr。"""
    seed_arr = sorted_nodes_by_glc[:k]
    
    print(f"\n种子集 seed_arr: {seed_arr}")
    
    # 计算IC影响力
    print(f"\n计算IC影响力...")
    start_time = time.time()
    
    # 调用您的mc_influence函数
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    total_time = time.time() - start_time
    
    # 显示结果
    print(f"\n" + "=" * 50)
    print(f"结果")
    print(f"=" * 50)
    print(f"网络: {network_name}")
    print(f"种子节点数 k: {k}")
    print(f"种子集: {seed_arr}")
    print(f"IC影响力: {ic_influence:.4f}")
    print(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    
    # 保存结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"glc_ic_test_k{k}_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("GLC算法 + IC模型 测试结果\n")
        f.write("=" * 40 + "\n")
        f.write(f"网络: {network_name}\n")
        f.write(f"种子节点数 k: {k}\n")
        f.write(f"传播概率 p: {p}\n")
        f.write(f"模拟次数: {NUM_SIMUS}\n")
        f.write(f"种子集: {seed_arr}\n")
        f.write(f"IC影响力: {ic_influence:.4f}\n")
        f.write(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%\n")
        f.write(f"\n")
        f.write("GLC节点排名:\n")
        f.write("-" * 25 + "\n")
        f.write(f"{'排名':<4} {'节点':<6} {'GLC值':<12}\n")
        f.write("-" * 25 + "\n")
        for i in range(min(10, len(sorted_nodes_by_glc))):
            node = sorted_nodes_by_glc[i]
            glc_value = glc.glc_values[node]
            f.write(f"{i+1:<4} {node:<6} {glc_value:<12.4f}\n")
    
    print(f"\n结果已保存到: {filename}")
    
    return ic_influence


if __name__ == "__main__":
    main()
