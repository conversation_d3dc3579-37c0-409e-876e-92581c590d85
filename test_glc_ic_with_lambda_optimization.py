"""
测试GLC算法 + IC模型 + λ*优化

正确的流程：
1. 加载网络
2. 进行λ*参数优化（计算最优λ值）
3. 使用最优λ*运行GLC算法
4. 选出前k个节点作为种子集
5. 计算IC影响力

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality, mc_influence
import networkx as nx
import time


def test_glc_ic_with_lambda_optimization():
    """
    测试完整的GLC + IC流程，包括λ*优化
    """
    print("GLC算法 + IC模型 + λ*优化 测试")
    print("=" * 60)
    
    # 使用Karate网络进行快速测试
    graph = nx.karate_club_graph()
    network_name = "karate"
    
    print(f"网络信息:")
    print(f"  网络: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    
    # 1. 初始化GLC算法
    print(f"\n1. 初始化GLC算法...")
    glc = GLCCentrality(graph, lambda_param=0.8)  # 使用默认值
    
    # 2. 进行λ*参数优化（简化版）
    print(f"\n2. 进行λ*参数优化...")
    print("根据论文方法计算最优λ*值...")
    
    # 使用较小的搜索范围和模拟次数进行快速测试
    lambda_range = [0.3, 0.5, 0.7, 0.9]  # 简化的搜索范围
    
    optimal_lambda = glc.optimize_lambda(
        lambda_range=lambda_range,
        num_simulations=50  # 减少模拟次数以加快测试
    )
    
    print(f"计算得到的最优λ*: {optimal_lambda}")
    
    # 3. 使用最优λ*重新运行GLC算法
    print(f"\n3. 使用最优λ*重新运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=optimal_lambda)
    glc.run_glc_algorithm()
    
    # 4. 获取节点评分排名
    print(f"\n4. 获取GLC节点评分排名...")
    sorted_nodes_by_glc = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    print(f"前10个节点的GLC评分:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i in range(min(10, len(sorted_nodes_by_glc))):
        node = sorted_nodes_by_glc[i]
        glc_value = glc.glc_values[node]
        print(f"{i+1:<4} {node:<6} {glc_value:<12.4f}")
    
    # 5. 选择前k个节点作为种子集
    k = 10  # 测试用
    print(f"\n5. 选择前{k}个节点作为种子集...")
    seed_arr = sorted_nodes_by_glc[:k]
    
    print(f"种子集 seed_arr: {seed_arr}")
    
    # 6. 设置IC模型参数
    p = 0.05
    NUM_SIMUS = 1000  # 测试用，减少模拟次数
    
    print(f"\n6. IC模型参数设置:")
    print(f"  种子节点数 k: {k}")
    print(f"  传播概率 p: {p}")
    print(f"  模拟次数: {NUM_SIMUS}")
    
    # 7. 计算IC影响力
    print(f"\n7. 计算IC影响力...")
    start_time = time.time()
    
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    total_time = time.time() - start_time
    
    # 8. 显示结果
    print(f"\n" + "=" * 60)
    print(f"GLC算法 + IC模型 结果")
    print(f"=" * 60)
    print(f"网络: {network_name}")
    print(f"计算得到的最优λ*: {optimal_lambda}")
    print(f"种子节点数 k: {k}")
    print(f"种子集: {seed_arr}")
    print(f"IC影响力: {ic_influence:.4f}")
    print(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    print(f"=" * 60)
    
    # 9. 保存结果
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"glc_ic_lambda_optimized_{network_name}_k{k}_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("GLC算法 + IC模型 + λ*优化 结果\n")
        f.write("=" * 50 + "\n")
        f.write(f"网络: {network_name}\n")
        f.write(f"节点数: {len(graph.nodes())}\n")
        f.write(f"边数: {len(graph.edges())}\n")
        f.write(f"计算得到的最优λ*: {optimal_lambda}\n")
        f.write(f"种子节点数 k: {k}\n")
        f.write(f"传播概率 p: {p}\n")
        f.write(f"模拟次数: {NUM_SIMUS}\n")
        f.write(f"种子集: {seed_arr}\n")
        f.write(f"IC影响力: {ic_influence:.4f}\n")
        f.write(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%\n")
        f.write(f"\n")
        f.write("说明:\n")
        f.write("- λ*参数通过优化算法计算得出\n")
        f.write("- GLC算法使用最优λ*值运行\n")
        f.write("- 种子集基于GLC排名选择\n")
        f.write("- IC影响力通过蒙特卡洛模拟计算\n")
    
    print(f"\n结果已保存到: {filename}")
    
    return {
        'optimal_lambda': optimal_lambda,
        'seed_arr': seed_arr,
        'ic_influence': ic_influence
    }


def compare_different_lambda_values():
    """
    比较不同λ值的影响
    """
    print(f"\n" + "=" * 60)
    print(f"比较不同λ值的影响")
    print(f"=" * 60)
    
    graph = nx.karate_club_graph()
    
    # 测试不同的λ值
    lambda_values = [0.3, 0.5, 0.7, 0.9]
    k = 10
    p = 0.05
    NUM_SIMUS = 500  # 快速测试
    
    results = {}
    
    for lambda_val in lambda_values:
        print(f"\n测试 λ = {lambda_val}...")
        
        # 使用指定λ值运行GLC算法
        glc = GLCCentrality(graph, lambda_param=lambda_val)
        glc.run_glc_algorithm()
        
        # 获取前k个节点
        sorted_nodes = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
        seed_arr = sorted_nodes[:k]
        
        # 计算IC影响力
        ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
        
        results[lambda_val] = {
            'seed_arr': seed_arr,
            'ic_influence': ic_influence
        }
        
        print(f"  λ = {lambda_val}: IC影响力 = {ic_influence:.4f}")
    
    # 显示比较结果
    print(f"\n" + "=" * 40)
    print(f"不同λ值的IC影响力比较")
    print(f"=" * 40)
    print(f"{'λ值':<6} {'IC影响力':<12} {'种子集前5个':<20}")
    print("-" * 40)
    
    for lambda_val in lambda_values:
        result = results[lambda_val]
        ic_influence = result['ic_influence']
        seed_preview = str(result['seed_arr'][:5])
        print(f"{lambda_val:<6} {ic_influence:<12.4f} {seed_preview:<20}")
    
    # 找到最佳λ值
    best_lambda = max(results.items(), key=lambda x: x[1]['ic_influence'])[0]
    best_influence = results[best_lambda]['ic_influence']
    
    print(f"\n最佳λ值: {best_lambda} (IC影响力: {best_influence:.4f})")
    
    return results


def main():
    """
    主函数
    """
    print("=" * 80)
    print("GLC算法 + IC模型 + λ*优化 完整测试")
    print("=" * 80)
    print("测试流程:")
    print("1. λ*参数优化（计算最优λ值）")
    print("2. 使用最优λ*运行GLC算法")
    print("3. 选择种子集并计算IC影响力")
    print("4. 比较不同λ值的效果")
    print("=" * 80)
    
    # 测试完整流程
    result = test_glc_ic_with_lambda_optimization()
    
    # 比较不同λ值
    comparison_results = compare_different_lambda_values()
    
    print(f"\n🎉 测试完成！")
    print(f"最优λ*: {result['optimal_lambda']}")
    print(f"IC影响力: {result['ic_influence']:.4f}")


if __name__ == "__main__":
    main()
