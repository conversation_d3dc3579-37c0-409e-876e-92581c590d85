"""
GLC算法 + IC模型 k=1到50 影响力评估专用脚本

这个脚本专门用于运行您需要的功能：
- p=0.05的传播概率
- 10000次IC模拟
- k=1到50的完整评估
- 结果保存到txt文件

使用方法：
python run_glc_ic_k_range.py

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import time


def run_k_range_evaluation():
    """
    运行k=1到50的IC影响力评估
    """
    print("GLC算法 + IC模型 k=1到50 影响力评估")
    print("=" * 80)
    
    # 1. 加载网络
    print("1. 加载网络...")
    try:
        # 尝试加载真实网络文件
        glc_temp = GLCCentrality(nx.Graph())
        
        # 按优先级尝试加载网络文件
        network_files = [
            ("networks/karate.txt", "karate"),
            ("networks/blog-int.txt", "blog"),
            ("networks/netscience-int.txt", "netscience"),
            ("networks/facebook_combined.txt", "facebook")
        ]
        
        graph = None
        network_name = None
        
        for filepath, name in network_files:
            try:
                graph = glc_temp.load_network_from_file(filepath)
                network_name = name
                print(f"成功加载网络文件: {filepath}")
                break
            except:
                continue
        
        if graph is None:
            # 如果所有文件都加载失败，使用内置网络
            print("网络文件未找到，使用Karate Club网络...")
            graph = nx.karate_club_graph()
            network_name = "karate_builtin"
    
    except Exception as e:
        print(f"加载网络时出错: {e}")
        print("使用Karate Club网络...")
        graph = nx.karate_club_graph()
        network_name = "karate_builtin"
    
    # 2. 显示网络信息
    print(f"\n网络基本信息:")
    print(f"  网络名称: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")
    print(f"  网络密度: {nx.density(graph):.4f}")
    
    # 3. 根据网络类型设置最优λ参数
    optimal_lambda_values = {
        'netscience': 1.0,
        'facebook': 0.95,
        'blog': 0.7,
        'karate': 0.5,
        'karate_builtin': 0.5
    }
    
    lambda_param = optimal_lambda_values.get(network_name, 0.8)
    print(f"  λ参数: {lambda_param}")
    
    # 4. 初始化GLC算法
    print(f"\n2. 初始化GLC算法...")
    glc = GLCCentrality(graph, lambda_param=lambda_param)
    
    # 5. 运行k=1到50的IC影响力评估
    print(f"\n3. 开始k=1到50的IC影响力评估...")
    print(f"参数设置:")
    print(f"  传播概率: p=0.05")
    print(f"  模拟次数: 10000")
    print(f"  评估范围: k=1到50")
    
    # 确定实际的k_max（不能超过网络节点数）
    k_max = min(50, len(graph.nodes()))
    print(f"  实际评估范围: k=1到{k_max}")
    
    # 开始评估
    start_time = time.time()
    
    try:
        results = glc.evaluate_k_range_ic_influence(
            k_max=k_max,
            p=0.05,
            num_simulations=10000,
            save_to_file=True,
            filename=f"glc_ic_k_range_{network_name}_results.txt"
        )
        
        total_time = time.time() - start_time
        
        # 6. 显示结果摘要
        print(f"\n4. 结果摘要:")
        print(f"=" * 60)
        print(f"评估完成！总用时: {total_time:.1f}秒")
        print(f"网络: {network_name}")
        print(f"λ参数: {lambda_param}")
        
        # 显示关键k值的结果
        key_k_values = [1, 5, 10, 20, 30, 40, 50]
        key_k_values = [k for k in key_k_values if k in results and k <= k_max]
        
        print(f"\n关键k值的IC影响力:")
        print(f"{'k值':<4} {'IC影响力':<12} {'增长率(%)':<12}")
        print("-" * 30)
        
        base_influence = results[1]
        for k in key_k_values:
            influence = results[k]
            growth_rate = (influence / base_influence - 1) * 100
            print(f"{k:<4} {influence:<12.4f} {growth_rate:<12.1f}")
        
        # 统计信息
        influences = list(results.values())
        max_influence = max(influences)
        min_influence = min(influences)
        avg_influence = sum(influences) / len(influences)
        
        print(f"\n统计信息:")
        print(f"  最大影响力: {max_influence:.4f} (k={k_max})")
        print(f"  最小影响力: {min_influence:.4f} (k=1)")
        print(f"  平均影响力: {avg_influence:.4f}")
        print(f"  总体增长率: {(max_influence / min_influence - 1) * 100:.1f}%")
        
        print(f"\n结果已保存到: glc_ic_k_range_{network_name}_results.txt")
        print(f"文件包含完整的k=1到{k_max}的IC影响力评估数据")
        
        return results
        
    except Exception as e:
        print(f"\n评估过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """
    主函数
    """
    print("=" * 80)
    print("GLC算法 + IC模型 k=1到50 影响力评估专用脚本")
    print("=" * 80)
    print("参数设置:")
    print("  传播概率: p=0.05 (固定)")
    print("  模拟次数: 10000 (固定)")
    print("  评估范围: k=1到50")
    print("=" * 80)
    
    # 运行评估
    results = run_k_range_evaluation()
    
    if results:
        print(f"\n🎉 评估成功完成！")
        print(f"您可以查看生成的txt文件获取详细结果。")
    else:
        print(f"\n❌ 评估失败，请检查错误信息。")


if __name__ == "__main__":
    main()
