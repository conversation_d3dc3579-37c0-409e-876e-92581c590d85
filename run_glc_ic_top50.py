"""
GLC中心性算法 + IC模型验证脚本

正确的思想：
1. GLC算法选出最具影响力的50个节点（中心性排名）
2. 对这50个节点分别计算实际的IC影响力
3. 验证GLC中心性算法的准确性

参数设置：
- p=0.05的传播概率
- 10000次IC模拟
- 评估前50个最重要的节点

使用方法：
python run_glc_ic_top50.py

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import time


def evaluate_top50_nodes_ic_influence():
    """
    评估GLC算法选出的前50个节点的IC影响力
    
    这是正确的中心性算法验证思想：
    1. GLC算法排名，选出前50个最重要节点
    2. 对每个节点单独计算IC影响力
    3. 比较GLC排名与IC影响力排名的一致性
    """
    print("GLC中心性算法 + IC模型验证")
    print("=" * 80)
    
    # 1. 加载网络
    print("1. 加载网络...")
    try:
        # 尝试加载真实网络文件
        glc_temp = GLCCentrality(nx.Graph())
        
        # 按优先级尝试加载网络文件
        network_files = [
            ("networks/karate.txt", "karate"),
            ("networks/blog-int.txt", "blog"),
            ("networks/netscience-int.txt", "netscience"),
            ("networks/facebook_combined.txt", "facebook")
        ]
        
        graph = None
        network_name = None
        
        for filepath, name in network_files:
            try:
                graph = glc_temp.load_network_from_file(filepath)
                network_name = name
                print(f"成功加载网络文件: {filepath}")
                break
            except:
                continue
        
        if graph is None:
            # 如果所有文件都加载失败，使用内置网络
            print("网络文件未找到，使用Karate Club网络...")
            graph = nx.karate_club_graph()
            network_name = "karate_builtin"
    
    except Exception as e:
        print(f"加载网络时出错: {e}")
        print("使用Karate Club网络...")
        graph = nx.karate_club_graph()
        network_name = "karate_builtin"
    
    # 2. 显示网络信息
    print(f"\n网络基本信息:")
    print(f"  网络名称: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")
    print(f"  网络密度: {nx.density(graph):.4f}")
    
    # 3. 根据网络类型设置最优λ参数
    optimal_lambda_values = {
        'netscience': 1.0,
        'facebook': 0.95,
        'blog': 0.7,
        'karate': 0.5,
        'karate_builtin': 0.5
    }
    
    lambda_param = optimal_lambda_values.get(network_name, 0.8)
    print(f"  λ参数: {lambda_param}")
    
    # 4. 初始化GLC算法
    print(f"\n2. 运行GLC中心性算法...")
    glc = GLCCentrality(graph, lambda_param=lambda_param)
    
    # 运行GLC算法
    glc.run_glc_algorithm()
    
    # 5. 获取前50个最重要的节点
    max_nodes = min(50, len(graph.nodes()))
    top_nodes = glc.get_top_k_nodes(max_nodes)
    
    print(f"\nGLC算法选出前{max_nodes}个最重要节点:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i, (node, glc_value) in enumerate(top_nodes[:10], 1):  # 只显示前10个
        print(f"{i:<4} {node:<6} {glc_value:<12.4f}")
    if max_nodes > 10:
        print(f"... (共{max_nodes}个节点)")
    
    # 6. 对每个节点计算IC影响力
    print(f"\n3. 计算每个节点的IC影响力...")
    print(f"参数设置:")
    print(f"  传播概率: p=0.05")
    print(f"  模拟次数: 10000")
    print(f"  评估节点数: {max_nodes}")
    
    # 初始化IC模型
    if glc.ic_model is None:
        glc.initialize_ic_model()
    
    # 计算每个节点的IC影响力
    ic_results = {}
    
    print(f"\n开始IC影响力计算...")
    start_time = time.time()
    
    for i, (node, glc_value) in enumerate(top_nodes, 1):
        print(f"\n计算节点{node} ({i}/{max_nodes})...")
        
        # 使用IC模型计算单个节点的影响力
        ic_influence = glc.ic_model.evaluate_node_influence(node, 0.05, 10000)
        
        ic_results[node] = {
            'glc_rank': i,
            'glc_value': glc_value,
            'ic_influence': ic_influence
        }
        
        print(f"  节点{node}: GLC值={glc_value:.4f}, IC影响力={ic_influence:.4f}")
        
        # 显示进度
        if i % 10 == 0:
            elapsed_time = time.time() - start_time
            avg_time_per_node = elapsed_time / i
            remaining_nodes = max_nodes - i
            estimated_remaining_time = avg_time_per_node * remaining_nodes
            
            print(f"\n进度报告:")
            print(f"  已完成: {i}/{max_nodes} ({i/max_nodes*100:.1f}%)")
            print(f"  已用时间: {elapsed_time:.1f}秒")
            print(f"  预计剩余时间: {estimated_remaining_time:.1f}秒")
    
    total_time = time.time() - start_time
    print(f"\n{'='*80}")
    print(f"IC影响力计算完成！总用时: {total_time:.1f}秒")
    print(f"{'='*80}")
    
    # 7. 分析结果并保存
    analyze_and_save_results(ic_results, network_name, lambda_param, max_nodes)
    
    return ic_results


def analyze_and_save_results(ic_results, network_name, lambda_param, max_nodes):
    """
    分析结果并保存到文件
    """
    print(f"\n4. 结果分析...")
    
    # 按IC影响力重新排序
    ic_ranking = sorted(ic_results.items(), key=lambda x: x[1]['ic_influence'], reverse=True)
    
    # 计算排名相关性
    glc_ranks = [ic_results[node]['glc_rank'] for node, _ in ic_ranking]
    ic_ranks = list(range(1, len(ic_ranking) + 1))
    
    # 简单的排名相关性分析
    rank_differences = [abs(glc_rank - ic_rank) for glc_rank, ic_rank in zip(glc_ranks, ic_ranks)]
    avg_rank_diff = sum(rank_differences) / len(rank_differences)
    
    print(f"\n排名一致性分析:")
    print(f"  平均排名差异: {avg_rank_diff:.2f}")
    print(f"  最大排名差异: {max(rank_differences)}")
    
    # 显示前10个节点的比较
    print(f"\n前10个节点的GLC vs IC比较:")
    print(f"{'IC排名':<6} {'节点':<6} {'GLC排名':<8} {'GLC值':<12} {'IC影响力':<12} {'排名差异':<8}")
    print("-" * 70)
    
    for ic_rank, (node, data) in enumerate(ic_ranking[:10], 1):
        glc_rank = data['glc_rank']
        glc_value = data['glc_value']
        ic_influence = data['ic_influence']
        rank_diff = abs(glc_rank - ic_rank)
        
        print(f"{ic_rank:<6} {node:<6} {glc_rank:<8} {glc_value:<12.4f} {ic_influence:<12.4f} {rank_diff:<8}")
    
    # 保存详细结果到文件
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"glc_ic_top{max_nodes}_results_{network_name}_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("GLC中心性算法 + IC模型验证结果\n")
        f.write("=" * 60 + "\n")
        f.write(f"网络信息:\n")
        f.write(f"  网络名称: {network_name}\n")
        f.write(f"  λ参数: {lambda_param}\n")
        f.write(f"  评估节点数: {max_nodes}\n")
        f.write(f"\n")
        f.write(f"IC模型参数:\n")
        f.write(f"  传播概率 p: 0.05\n")
        f.write(f"  模拟次数: 10000\n")
        f.write(f"\n")
        f.write(f"排名一致性分析:\n")
        f.write(f"  平均排名差异: {avg_rank_diff:.2f}\n")
        f.write(f"  最大排名差异: {max(rank_differences)}\n")
        f.write(f"\n")
        
        # 写入详细结果
        f.write("详细结果 (按IC影响力排序):\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'IC排名':<6} {'节点':<6} {'GLC排名':<8} {'GLC值':<12} {'IC影响力':<12} {'排名差异':<8}\n")
        f.write("-" * 80 + "\n")
        
        for ic_rank, (node, data) in enumerate(ic_ranking, 1):
            glc_rank = data['glc_rank']
            glc_value = data['glc_value']
            ic_influence = data['ic_influence']
            rank_diff = abs(glc_rank - ic_rank)
            
            f.write(f"{ic_rank:<6} {node:<6} {glc_rank:<8} {glc_value:<12.4f} {ic_influence:<12.4f} {rank_diff:<8}\n")
    
    print(f"\n详细结果已保存到: {filename}")
    print(f"文件包含前{max_nodes}个节点的完整GLC vs IC比较数据")


def main():
    """
    主函数
    """
    print("=" * 80)
    print("GLC中心性算法验证：前50个节点的IC影响力评估")
    print("=" * 80)
    print("算法思想:")
    print("1. GLC算法选出最具影响力的50个节点（中心性排名）")
    print("2. 对这50个节点分别计算实际的IC影响力")
    print("3. 验证GLC中心性算法的准确性")
    print("=" * 80)
    
    # 运行评估
    results = evaluate_top50_nodes_ic_influence()
    
    if results:
        print(f"\n🎉 验证成功完成！")
        print(f"GLC中心性算法的准确性已通过IC模型验证。")
    else:
        print(f"\n❌ 验证失败，请检查错误信息。")


if __name__ == "__main__":
    main()
