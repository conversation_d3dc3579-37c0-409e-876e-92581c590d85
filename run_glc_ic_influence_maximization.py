"""
GLC算法 + IC模型 影响力最大化问题

正确的思想：
1. GLC算法选出前50个最重要的节点作为种子集
2. 计算这50个节点共同的IC影响力（影响力最大化）
3. 这是经典的影响力最大化问题

参数设置：
- p=0.05的传播概率
- 10000次IC模拟
- k=50个种子节点

使用方法：
python run_glc_ic_influence_maximization.py

作者：基于GLC_IC.py实现
"""

from GLC_IC import GLCCentrality
import networkx as nx
import time


def run_influence_maximization():
    """
    运行GLC算法的影响力最大化
    
    这是正确的影响力最大化思想：
    1. GLC算法选出前k个最重要节点作为种子集
    2. 计算这k个节点共同的IC影响力
    3. 解决影响力最大化问题
    """
    print("GLC算法 + IC模型 影响力最大化问题")
    print("=" * 80)
    
    # 1. 加载网络
    print("1. 加载网络...")
    try:
        # 尝试加载真实网络文件
        glc_temp = GLCCentrality(nx.Graph())
        
        # 按优先级尝试加载网络文件
        network_files = [
            ("networks/karate.txt", "karate"),
            ("networks/blog-int.txt", "blog"),
            ("networks/netscience-int.txt", "netscience"),
            ("networks/facebook_combined.txt", "facebook")
        ]
        
        graph = None
        network_name = None
        
        for filepath, name in network_files:
            try:
                graph = glc_temp.load_network_from_file(filepath)
                network_name = name
                print(f"成功加载网络文件: {filepath}")
                break
            except:
                continue
        
        if graph is None:
            # 如果所有文件都加载失败，使用内置网络
            print("网络文件未找到，使用Karate Club网络...")
            graph = nx.karate_club_graph()
            network_name = "karate_builtin"
    
    except Exception as e:
        print(f"加载网络时出错: {e}")
        print("使用Karate Club网络...")
        graph = nx.karate_club_graph()
        network_name = "karate_builtin"
    
    # 2. 显示网络信息
    print(f"\n网络基本信息:")
    print(f"  网络名称: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    print(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}")
    print(f"  网络密度: {nx.density(graph):.4f}")
    
    # 3. 根据网络类型设置最优λ参数
    optimal_lambda_values = {
        'netscience': 1.0,
        'facebook': 0.95,
        'blog': 0.7,
        'karate': 0.5,
        'karate_builtin': 0.5
    }
    
    lambda_param = optimal_lambda_values.get(network_name, 0.8)
    print(f"  λ参数: {lambda_param}")
    
    # 4. 初始化GLC算法
    print(f"\n2. 运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=lambda_param)
    
    # 运行GLC算法
    glc.run_glc_algorithm()
    
    # 5. 选择种子节点数k
    k = min(50, len(graph.nodes()))
    print(f"\n影响力最大化参数:")
    print(f"  种子节点数 k: {k}")
    print(f"  传播概率 p: 0.05")
    print(f"  模拟次数: 10000")
    
    # 6. 获取前k个最重要的节点作为种子集
    top_k_nodes = glc.get_top_k_nodes(k)
    seed_set = [node for node, _ in top_k_nodes]
    
    print(f"\n3. GLC算法选出的种子集 (k={k}):")
    print(f"种子节点: {seed_set}")
    
    # 显示前10个节点的GLC值
    print(f"\n前10个种子节点的GLC值:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i, (node, glc_value) in enumerate(top_k_nodes[:10], 1):
        print(f"{i:<4} {node:<6} {glc_value:<12.4f}")
    if k > 10:
        print(f"... (共{k}个种子节点)")
    
    # 7. 计算种子集的IC影响力
    print(f"\n4. 计算种子集的IC影响力...")
    
    # 初始化IC模型
    if glc.ic_model is None:
        glc.initialize_ic_model()
    
    print(f"开始IC模拟...")
    start_time = time.time()
    
    # 使用IC模型计算种子集的总影响力
    total_influence = glc.ic_model.mc_influence(seed_set, 0.05, 10000)
    
    total_time = time.time() - start_time
    
    print(f"\n{'='*80}")
    print(f"影响力最大化结果")
    print(f"{'='*80}")
    print(f"网络: {network_name}")
    print(f"λ参数: {lambda_param}")
    print(f"种子节点数 k: {k}")
    print(f"种子集: {seed_set}")
    print(f"总影响力: {total_influence:.4f}")
    print(f"影响力比例: {total_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    print(f"{'='*80}")
    
    # 8. 保存结果
    save_influence_maximization_results(
        network_name, lambda_param, k, seed_set, 
        total_influence, top_k_nodes, graph
    )
    
    return {
        'network_name': network_name,
        'k': k,
        'seed_set': seed_set,
        'total_influence': total_influence,
        'influence_ratio': total_influence/len(graph.nodes())*100
    }


def save_influence_maximization_results(network_name, lambda_param, k, seed_set, 
                                       total_influence, top_k_nodes, graph):
    """
    保存影响力最大化结果到文件
    """
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"glc_influence_maximization_{network_name}_k{k}_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("GLC算法 + IC模型 影响力最大化结果\n")
        f.write("=" * 60 + "\n")
        f.write(f"网络信息:\n")
        f.write(f"  网络名称: {network_name}\n")
        f.write(f"  节点数: {len(graph.nodes())}\n")
        f.write(f"  边数: {len(graph.edges())}\n")
        f.write(f"  平均度数: {sum(dict(graph.degree()).values()) / len(graph.nodes()):.2f}\n")
        f.write(f"  网络密度: {nx.density(graph):.4f}\n")
        f.write(f"\n")
        f.write(f"GLC算法参数:\n")
        f.write(f"  λ参数: {lambda_param}\n")
        f.write(f"\n")
        f.write(f"影响力最大化参数:\n")
        f.write(f"  种子节点数 k: {k}\n")
        f.write(f"  传播概率 p: 0.05\n")
        f.write(f"  模拟次数: 10000\n")
        f.write(f"\n")
        f.write(f"影响力最大化结果:\n")
        f.write(f"  种子集: {seed_set}\n")
        f.write(f"  总影响力: {total_influence:.4f}\n")
        f.write(f"  影响力比例: {total_influence/len(graph.nodes())*100:.2f}%\n")
        f.write(f"\n")
        
        # 写入种子节点详细信息
        f.write("种子节点详细信息:\n")
        f.write("-" * 40 + "\n")
        f.write(f"{'排名':<4} {'节点':<6} {'GLC值':<12}\n")
        f.write("-" * 40 + "\n")
        
        for i, (node, glc_value) in enumerate(top_k_nodes, 1):
            f.write(f"{i:<4} {node:<6} {glc_value:<12.4f}\n")
        
        f.write(f"\n")
        f.write(f"说明:\n")
        f.write(f"- 这是经典的影响力最大化问题\n")
        f.write(f"- GLC算法选出前{k}个最重要节点作为种子集\n")
        f.write(f"- IC模型计算种子集的总传播影响力\n")
        f.write(f"- 总影响力表示在IC模型下平均能影响多少个节点\n")
    
    print(f"\n结果已保存到: {filename}")
    print(f"文件包含完整的影响力最大化结果")


def main():
    """
    主函数
    """
    print("=" * 80)
    print("GLC算法影响力最大化问题")
    print("=" * 80)
    print("问题描述:")
    print("- 给定网络和种子节点数k")
    print("- 选择k个节点作为种子集")
    print("- 最大化IC模型下的影响传播")
    print("- GLC算法用于选择最优种子集")
    print("=" * 80)
    
    # 运行影响力最大化
    results = run_influence_maximization()
    
    if results:
        print(f"\n🎉 影响力最大化完成！")
        print(f"种子节点数: {results['k']}")
        print(f"总影响力: {results['total_influence']:.4f}")
        print(f"影响力比例: {results['influence_ratio']:.2f}%")
    else:
        print(f"\n❌ 影响力最大化失败，请检查错误信息。")


if __name__ == "__main__":
    main()
