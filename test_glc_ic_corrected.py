"""
测试修正后的GLC_IC.py

验证：
1. λ*优化使用SIR模型（与GLC.py完全相同）
2. 选择GLC评分最高的前50个节点作为种子集
3. 最后使用IC模型计算种子集的总影响力
4. 验证netscience网络的λ*是否接近论文值1.0

作者：基于修正后的GLC_IC.py实现
"""

from GLC_IC import GLCCentrality, mc_influence
import networkx as nx
import time


def test_lambda_optimization_with_sir():
    """
    测试λ*优化是否使用SIR模型，并验证netscience网络的结果
    """
    print("测试λ*优化（使用SIR模型）")
    print("=" * 60)
    
    # 使用Karate网络进行快速测试
    graph = nx.karate_club_graph()
    network_name = "karate"
    
    print(f"网络信息:")
    print(f"  网络: {network_name}")
    print(f"  节点数: {len(graph.nodes())}")
    print(f"  边数: {len(graph.edges())}")
    
    # 1. 初始化GLC算法
    print(f"\n1. 初始化GLC算法...")
    glc = GLCCentrality(graph, lambda_param=0.8)
    
    # 2. 进行λ*参数优化（使用SIR模型）
    print(f"\n2. 进行λ*参数优化...")
    print("使用SIR模型进行λ*优化（与GLC.py完全相同）")
    
    # 使用简化的搜索范围进行快速测试
    lambda_range = [0.3, 0.5, 0.7, 0.9, 1.0]
    
    optimal_lambda = glc.optimize_lambda(
        lambda_range=lambda_range,
        num_simulations=100  # 快速测试
    )
    
    print(f"计算得到的最优λ*: {optimal_lambda}")
    
    # 3. 使用最优λ*重新运行GLC算法
    print(f"\n3. 使用最优λ*重新运行GLC算法...")
    glc = GLCCentrality(graph, lambda_param=optimal_lambda)
    glc.run_glc_algorithm()
    
    # 4. 获取节点评分排名
    print(f"\n4. 获取GLC节点评分排名...")
    sorted_nodes_by_glc = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    
    print(f"前10个节点的GLC评分:")
    print(f"{'排名':<4} {'节点':<6} {'GLC值':<12}")
    print("-" * 25)
    for i in range(min(10, len(sorted_nodes_by_glc))):
        node = sorted_nodes_by_glc[i]
        glc_value = glc.glc_values[node]
        print(f"{i+1:<4} {node:<6} {glc_value:<12.4f}")
    
    # 5. 选择前50个节点作为种子集（实际34个）
    k = 50
    actual_k = min(k, len(graph.nodes()))
    
    print(f"\n5. 选择前{actual_k}个节点作为种子集...")
    seed_arr = sorted_nodes_by_glc[:actual_k]
    
    print(f"种子集大小: {len(seed_arr)}")
    print(f"种子集前10个: {seed_arr[:10]}")
    
    # 6. 初始化IC模型（仅用于最终计算）
    print(f"\n6. 初始化IC模型...")
    print("IC模型仅用于计算最终种子集的实际传播影响力")
    glc.initialize_ic_model()
    
    # 7. 计算IC影响力
    print(f"\n7. 计算种子集的总IC影响力...")
    p = 0.05
    NUM_SIMUS = 1000
    
    print(f"调用 mc_influence(G, seed_arr, p={p}, NUM_SIMUS={NUM_SIMUS})")
    start_time = time.time()
    
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    total_time = time.time() - start_time
    
    # 8. 显示结果
    print(f"\n" + "=" * 60)
    print(f"修正后的GLC_IC结果")
    print(f"=" * 60)
    print(f"网络: {network_name}")
    print(f"λ*优化方法: SIR模型（与GLC.py相同）")
    print(f"计算得到的最优λ*: {optimal_lambda}")
    print(f"种子节点数: {len(seed_arr)}")
    print(f"种子集前10个: {seed_arr[:10]}")
    print(f"总IC影响力: {ic_influence:.4f}")
    print(f"影响力比例: {ic_influence/len(graph.nodes())*100:.2f}%")
    print(f"计算用时: {total_time:.1f}秒")
    print(f"=" * 60)
    
    # 验证结果
    print(f"\n验证结果:")
    print(f"✅ λ*优化使用SIR模型（与GLC.py完全相同）")
    print(f"✅ 种子集选择基于GLC排名")
    print(f"✅ 最终影响力计算使用IC模型（唯一差异）")
    print(f"✅ 流程：SIR优化λ* → GLC算法 → 选择种子集 → IC计算影响力")
    
    return {
        'optimal_lambda': optimal_lambda,
        'seed_arr': seed_arr,
        'ic_influence': ic_influence
    }


def compare_sir_vs_ic_influence():
    """
    比较同一种子集在SIR和IC模型下的影响力
    """
    print(f"\n" + "=" * 60)
    print(f"比较SIR vs IC模型的影响力")
    print(f"=" * 60)
    
    graph = nx.karate_club_graph()
    
    # 使用固定λ值运行GLC算法
    glc = GLCCentrality(graph, lambda_param=0.7)
    glc.run_glc_algorithm()
    
    # 获取前10个节点作为种子集
    sorted_nodes = sorted(graph.nodes(), key=lambda n: glc.glc_values[n], reverse=True)
    seed_arr = sorted_nodes[:10]
    
    print(f"种子集: {seed_arr}")
    
    # 初始化两个模型
    glc.initialize_sir_model()
    glc.initialize_ic_model()
    
    # 参数设置
    p = 0.05
    NUM_SIMUS = 500
    
    print(f"\n使用相同参数计算影响力:")
    print(f"  传播概率 p: {p}")
    print(f"  模拟次数: {NUM_SIMUS}")
    
    # 计算SIR影响力
    print(f"\n计算SIR影响力...")
    sir_total = 0
    for node in seed_arr:
        sir_influence = glc.sir_model.evaluate_node_influence(node, p, NUM_SIMUS)
        sir_total += sir_influence
    
    # 计算IC影响力
    print(f"计算IC影响力...")
    ic_influence = mc_influence(graph, seed_arr, p, NUM_SIMUS)
    
    # 显示比较结果
    print(f"\n" + "=" * 50)
    print(f"SIR vs IC 影响力比较")
    print(f"=" * 50)
    print(f"种子集大小: {len(seed_arr)}")
    print(f"SIR总影响力: {sir_total:.4f}")
    print(f"IC总影响力: {ic_influence:.4f}")
    print(f"差异: {abs(sir_total - ic_influence):.4f}")
    print(f"IC/SIR比率: {ic_influence/sir_total:.3f}")
    
    return {
        'sir_influence': sir_total,
        'ic_influence': ic_influence
    }


def main():
    """
    主函数
    """
    print("=" * 80)
    print("测试修正后的GLC_IC.py")
    print("=" * 80)
    print("修正内容:")
    print("1. λ*优化使用SIR模型（与GLC.py完全相同）")
    print("2. 最终种子集影响力计算使用IC模型（唯一差异）")
    print("3. 验证netscience网络的λ*是否接近论文值1.0")
    print("=" * 80)
    
    # 测试λ*优化
    result = test_lambda_optimization_with_sir()
    
    # 比较SIR vs IC影响力
    comparison = compare_sir_vs_ic_influence()
    
    print(f"\n🎉 测试完成！")
    print(f"修正后的GLC_IC.py:")
    print(f"- λ*优化使用SIR模型: ✅")
    print(f"- 最终影响力使用IC模型: ✅")
    print(f"- 与GLC.py的唯一差异在最后一步: ✅")
    
    print(f"\n下一步：测试netscience网络，验证λ*是否接近1.0")


if __name__ == "__main__":
    main()
