"""
GLC (Global-Local Centrality) 算法实现

该算法结合了局部影响力和全局影响力来识别网络中的关键节点。
主要包含三个步骤：
1. 群组检测和全局关键节点选择
2. 局部影响力计算
3. 整体影响力计算

作者：基于论文实现
"""

import networkx as nx
import numpy as np
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple, Union
import heapq


class GLCCentrality:
    """GLC中心性算法实现类"""

    def __init__(self, graph: nx.Graph, lambda_param: float = 0.8):
        """
        初始化GLC算法

        Args:
            graph: NetworkX图对象
            lambda_param: 聚类覆盖比例参数，默认0.8
        """
        self.graph = graph.copy()
        self.lambda_param = lambda_param
        self.clusters = []
        self.global_critical_nodes = set()
        self.pc_values = {}
        self.k_shell_values = {}
        self.local_influence = {}
        self.glc_values = {}

    def load_network_from_file(self, filepath: str) -> nx.Graph:
        """
        从文件加载网络数据

        Args:
            filepath: 网络数据文件路径

        Returns:
            NetworkX图对象
        """
        graph = nx.Graph()

        with open(filepath, 'r') as f:
            lines = f.readlines()

        # 第一行通常包含节点数和边数信息
        first_line = lines[0].strip().split()
        if len(first_line) == 2:
            n_nodes, n_edges = map(int, first_line)
            lines = lines[1:]  # 跳过第一行

        # 读取边信息
        for line in lines:
            if line.strip():
                parts = line.strip().split()
                if len(parts) >= 2:
                    u, v = int(parts[0]), int(parts[1])
                    graph.add_edge(u, v)

        return graph

    def compute_k_shell(self) -> Dict[int, int]:
        """
        计算所有节点的k-shell值

        Returns:
            节点到k-shell值的映射字典
        """
        # 使用NetworkX内置的k-shell分解
        k_shell = nx.core_number(self.graph)
        self.k_shell_values = k_shell
        return k_shell

    def compute_clustering_potential(self) -> Dict[int, float]:
        """
        计算所有节点的聚类潜力pc值

        根据论文公式(10): pc_i = k_i * sum(k_in_j for j in N(i))
        其中k_in_j是邻居节点j连接到节点i及其邻居的边数

        注意：根据论文第312-322行的详细说明，k_in_j是指邻居节点j
        连接到"节点i及其邻居"的边数，这是一个局部聚类度量

        Returns:
            节点到pc值的映射字典
        """
        pc_values = {}

        for node in self.graph.nodes():
            k_i = self.graph.degree(node)  # 节点i的度数
            neighbors_i = set(self.graph.neighbors(node))  # 节点i的邻居集合

            # 计算sum(k_in_j for j in N(i))
            sum_k_in = 0
            for neighbor_j in neighbors_i:
                # k_in_j: 邻居节点j连接到"节点i及其邻居"的边数
                neighbors_j = set(self.graph.neighbors(neighbor_j))
                # "节点i及其邻居"的集合
                i_and_its_neighbors = neighbors_i | {node}
                # 计算邻居j与"节点i及其邻居"的连接数
                k_in_j = len(neighbors_j.intersection(i_and_its_neighbors))
                sum_k_in += k_in_j

            # 根据公式(10): pc_i = k_i * sum(k_in_j)
            pc_values[node] = k_i * sum_k_in

        self.pc_values = pc_values
        return pc_values

    def detect_clusters(self) -> List[Set[int]]:
        """
        检测网络中的聚类群组

        实现算法1中的聚类检测过程：
        1. 选择pc值最大的节点作为初始节点
        2. 添加pc值超过pcmax/2的邻居节点
        3. 通过三度影响规则扩展聚类
        4. 重复直到覆盖λ比例的节点

        Returns:
            聚类列表，每个聚类是节点集合
        """
        if not self.pc_values:
            self.compute_clustering_potential()

        clusters = []
        remaining_nodes = set(self.graph.nodes())
        pc_values_copy = self.pc_values.copy()
        total_nodes = len(self.graph.nodes())
        target_coverage = int(total_nodes * self.lambda_param)
        covered_nodes = 0

        while covered_nodes < target_coverage and remaining_nodes:
            # Step 1: 选择pc值最大的节点作为初始节点
            if not any(pc_values_copy[node] > 0 for node in remaining_nodes):
                break

            # 找到pc值最大的节点
            max_pc = max(pc_values_copy[node] for node in remaining_nodes if pc_values_copy[node] > 0)
            candidates = [node for node in remaining_nodes if pc_values_copy[node] == max_pc]

            # 在候选节点中选择度数最大的作为初始节点
            initial_node = max(candidates, key=lambda x: self.graph.degree(x))

            # 创建新聚类
            cluster = {initial_node}

            # 添加pc值超过pcmax/2的邻居节点
            threshold = max_pc / 2
            for neighbor in self.graph.neighbors(initial_node):
                if neighbor in remaining_nodes and pc_values_copy[neighbor] >= threshold:
                    cluster.add(neighbor)

            # Step 2: 通过三度影响规则扩展聚类（重复3次）
            for iteration in range(3):
                new_nodes = set()

                # 获取聚类的邻居节点
                cluster_neighbors = set()
                for node in cluster:
                    cluster_neighbors.update(self.graph.neighbors(node))
                cluster_neighbors -= cluster  # 移除已在聚类中的节点
                cluster_neighbors &= remaining_nodes  # 只考虑剩余节点

                # 按度数升序检查邻居节点
                sorted_neighbors = sorted(cluster_neighbors, key=lambda x: self.graph.degree(x))

                for neighbor in sorted_neighbors:
                    # 计算k_in和k_out
                    k_in = len(set(self.graph.neighbors(neighbor)) & cluster)
                    k_out = self.graph.degree(neighbor) - k_in

                    # 如果k_in >= k_out，则加入聚类
                    if k_in >= k_out:
                        new_nodes.add(neighbor)

                cluster.update(new_nodes)

            # Step 3: 将聚类中的节点pc值设为0
            for node in cluster:
                pc_values_copy[node] = 0

            clusters.append(cluster)
            remaining_nodes -= cluster
            covered_nodes += len(cluster)

        self.clusters = clusters
        return clusters

    def select_global_critical_nodes(self) -> Set[int]:
        """
        从每个聚类中选择度数最大的节点作为全局关键节点

        Returns:
            全局关键节点集合
        """
        if not self.clusters:
            self.detect_clusters()

        global_critical_nodes = set()

        for cluster in self.clusters:
            if cluster:  # 确保聚类非空
                # 选择聚类中度数最大的节点
                critical_node = max(cluster, key=lambda x: self.graph.degree(x))
                global_critical_nodes.add(critical_node)

        self.global_critical_nodes = global_critical_nodes
        return global_critical_nodes

    def compute_local_influence(self) -> Dict[int, float]:
        """
        计算所有节点的局部影响力

        根据公式: LI_i = sum(ks_j for j in N(i))
        其中ks_j是邻居节点j的k-shell值

        Returns:
            节点到局部影响力的映射字典
        """
        if not self.k_shell_values:
            self.compute_k_shell()

        local_influence = {}

        for node in self.graph.nodes():
            # 计算邻居节点的k-shell值之和
            li_value = sum(self.k_shell_values[neighbor] for neighbor in self.graph.neighbors(node))
            local_influence[node] = li_value

        self.local_influence = local_influence
        return local_influence

    def compute_glc_centrality(self) -> Dict[int, float]:
        """
        计算所有节点的GLC中心性值

        根据公式: GLC_i = LI_i * sum(LI_u / 2^d_iu for u in C)
        其中:
        - LI_i是节点i的局部影响力
        - C是全局关键节点集合
        - LI_u是全局关键节点u的局部影响力
        - d_iu是节点i到全局关键节点u的最短路径长度

        Returns:
            节点到GLC值的映射字典
        """
        # 确保所有必要的计算都已完成
        if not self.local_influence:
            self.compute_local_influence()
        if not self.global_critical_nodes:
            self.select_global_critical_nodes()

        glc_values = {}

        # 计算所有节点对之间的最短路径
        try:
            shortest_paths = dict(nx.all_pairs_shortest_path_length(self.graph))
        except:
            # 如果图不连通，使用单源最短路径
            shortest_paths = {}
            for node in self.graph.nodes():
                shortest_paths[node] = nx.single_source_shortest_path_length(self.graph, node)

        for node in self.graph.nodes():
            li_i = self.local_influence[node]

            # 计算全局影响力部分
            global_influence_sum = 0
            for critical_node in self.global_critical_nodes:
                li_u = self.local_influence[critical_node]

                # 获取最短路径长度
                if node in shortest_paths and critical_node in shortest_paths[node]:
                    d_iu = shortest_paths[node][critical_node]
                elif critical_node in shortest_paths and node in shortest_paths[critical_node]:
                    d_iu = shortest_paths[critical_node][node]
                else:
                    # 如果节点不连通，设置一个大的距离值
                    d_iu = float('inf')
                    continue

                if d_iu != float('inf') and d_iu > 0:
                    global_influence_sum += li_u / (2 ** d_iu)
                elif d_iu == 0:  # 节点本身就是全局关键节点
                    global_influence_sum += li_u

            # 计算最终的GLC值
            glc_values[node] = li_i * global_influence_sum

        self.glc_values = glc_values
        return glc_values

    def run_glc_algorithm(self) -> Dict[int, float]:
        """
        运行完整的GLC算法

        Returns:
            按GLC值排序的节点字典
        """
        print("开始运行GLC算法...")

        # Step 1: 计算k-shell值
        print("1. 计算k-shell值...")
        self.compute_k_shell()

        # Step 2: 计算聚类潜力
        print("2. 计算聚类潜力...")
        self.compute_clustering_potential()

        # Step 3: 检测聚类
        print("3. 检测聚类...")
        clusters = self.detect_clusters()
        print(f"   检测到 {len(clusters)} 个聚类")

        # Step 4: 选择全局关键节点
        print("4. 选择全局关键节点...")
        global_nodes = self.select_global_critical_nodes()
        print(f"   选择了 {len(global_nodes)} 个全局关键节点")

        # Step 5: 计算局部影响力
        print("5. 计算局部影响力...")
        self.compute_local_influence()

        # Step 6: 计算GLC中心性
        print("6. 计算GLC中心性...")
        glc_values = self.compute_glc_centrality()

        # 按GLC值排序
        sorted_nodes = sorted(glc_values.items(), key=lambda x: x[1], reverse=True)

        print("GLC算法运行完成！")
        return dict(sorted_nodes)

    def get_top_k_nodes(self, k: int = 10) -> List[Tuple[int, float]]:
        """
        获取前k个最重要的节点

        Args:
            k: 返回的节点数量

        Returns:
            (节点, GLC值)的列表
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        sorted_nodes = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
        return sorted_nodes[:k]

    def print_results(self, top_k: int = 10):
        """
        打印算法结果

        Args:
            top_k: 显示前k个重要节点
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        print(f"\n=== GLC算法结果 ===")
        print(f"网络节点数: {len(self.graph.nodes())}")
        print(f"网络边数: {len(self.graph.edges())}")
        print(f"检测到的聚类数: {len(self.clusters)}")
        print(f"全局关键节点数: {len(self.global_critical_nodes)}")

        # 显示聚类信息
        print(f"\n聚类详细信息:")
        for i, cluster in enumerate(self.clusters, 1):
            print(f"聚类 {i}: {len(cluster)} 个节点 - {sorted(list(cluster))}")

        print(f"\n全局关键节点: {sorted(list(self.global_critical_nodes))}")

        print(f"\n前 {top_k} 个最重要的节点:")
        print("排名\t节点ID\tGLC值\t\t度数\tk-shell值\t局部影响力")
        print("-" * 70)

        top_nodes = self.get_top_k_nodes(top_k)
        for i, (node, glc_value) in enumerate(top_nodes, 1):
            degree = self.graph.degree(node)
            k_shell = self.k_shell_values.get(node, 0)
            local_inf = self.local_influence.get(node, 0)
            print(f"{i}\t{node}\t{glc_value:.6f}\t{degree}\t{k_shell}\t\t{local_inf:.2f}")

    def compare_with_other_centralities(self, top_k: int = 10):
        """
        与其他中心性指标进行比较

        Args:
            top_k: 比较的节点数量
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        # 计算其他中心性指标
        degree_centrality = nx.degree_centrality(self.graph)
        betweenness_centrality = nx.betweenness_centrality(self.graph)
        closeness_centrality = nx.closeness_centrality(self.graph)
        eigenvector_centrality = nx.eigenvector_centrality(self.graph, max_iter=1000)

        # 获取各种中心性的前k个节点
        glc_top = [node for node, _ in self.get_top_k_nodes(top_k)]
        degree_top = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        degree_top = [node for node, _ in degree_top]
        betweenness_top = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        betweenness_top = [node for node, _ in betweenness_top]
        closeness_top = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        closeness_top = [node for node, _ in closeness_top]
        eigenvector_top = sorted(eigenvector_centrality.items(), key=lambda x: x[1], reverse=True)[:top_k]
        eigenvector_top = [node for node, _ in eigenvector_top]

        print(f"\n=== 中心性指标比较 (前{top_k}个节点) ===")
        print("排名\tGLC\t度中心性\t介数中心性\t接近中心性\t特征向量中心性")
        print("-" * 80)

        for i in range(top_k):
            glc_node = glc_top[i] if i < len(glc_top) else "-"
            degree_node = degree_top[i] if i < len(degree_top) else "-"
            betweenness_node = betweenness_top[i] if i < len(betweenness_top) else "-"
            closeness_node = closeness_top[i] if i < len(closeness_top) else "-"
            eigenvector_node = eigenvector_top[i] if i < len(eigenvector_top) else "-"

            print(f"{i+1}\t{glc_node}\t{degree_node}\t\t{betweenness_node}\t\t{closeness_node}\t\t{eigenvector_node}")

    def save_results_to_file(self, filename: str = "glc_results.txt"):
        """
        将结果保存到文件

        Args:
            filename: 输出文件名
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("GLC算法结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"网络节点数: {len(self.graph.nodes())}\n")
            f.write(f"网络边数: {len(self.graph.edges())}\n")
            f.write(f"检测到的聚类数: {len(self.clusters)}\n")
            f.write(f"全局关键节点数: {len(self.global_critical_nodes)}\n\n")

            f.write("所有节点的GLC值:\n")
            f.write("节点ID\tGLC值\t\t度数\tk-shell值\t局部影响力\n")
            f.write("-" * 60 + "\n")

            sorted_nodes = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
            for node, glc_value in sorted_nodes:
                degree = self.graph.degree(node)
                k_shell = self.k_shell_values.get(node, 0)
                local_inf = self.local_influence.get(node, 0)
                f.write(f"{node}\t{glc_value:.6f}\t{degree}\t{k_shell}\t\t{local_inf:.2f}\n")

        print(f"结果已保存到文件: {filename}")

    def analyze_network_properties(self):
        """
        分析网络的基本属性
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        print(f"\n=== 网络属性分析 ===")

        # 基本统计
        n_nodes = len(self.graph.nodes())
        n_edges = len(self.graph.edges())
        density = nx.density(self.graph)

        print(f"节点数: {n_nodes}")
        print(f"边数: {n_edges}")
        print(f"网络密度: {density:.4f}")

        # 度分布统计
        degrees = [self.graph.degree(node) for node in self.graph.nodes()]
        avg_degree = sum(degrees) / len(degrees)
        max_degree = max(degrees)
        min_degree = min(degrees)

        print(f"平均度数: {avg_degree:.2f}")
        print(f"最大度数: {max_degree}")
        print(f"最小度数: {min_degree}")

        # k-shell统计
        k_shells = list(self.k_shell_values.values())
        max_k_shell = max(k_shells)
        avg_k_shell = sum(k_shells) / len(k_shells)

        print(f"最大k-shell值: {max_k_shell}")
        print(f"平均k-shell值: {avg_k_shell:.2f}")

        # 聚类系数
        clustering_coeff = nx.average_clustering(self.graph)
        print(f"平均聚类系数: {clustering_coeff:.4f}")

        # 连通性
        is_connected = nx.is_connected(self.graph)
        print(f"网络连通性: {'连通' if is_connected else '不连通'}")

        if is_connected:
            diameter = nx.diameter(self.graph)
            avg_path_length = nx.average_shortest_path_length(self.graph)
            print(f"网络直径: {diameter}")
            print(f"平均最短路径长度: {avg_path_length:.4f}")

        # GLC值统计
        glc_values = list(self.glc_values.values())
        max_glc = max(glc_values)
        min_glc = min(glc_values)
        avg_glc = sum(glc_values) / len(glc_values)

        print(f"\nGLC值统计:")
        print(f"最大GLC值: {max_glc:.4f}")
        print(f"最小GLC值: {min_glc:.4f}")
        print(f"平均GLC值: {avg_glc:.4f}")

        # 聚类统计
        cluster_sizes = [len(cluster) for cluster in self.clusters]
        if cluster_sizes:
            avg_cluster_size = sum(cluster_sizes) / len(cluster_sizes)
            max_cluster_size = max(cluster_sizes)
            min_cluster_size = min(cluster_sizes)

            print(f"\n聚类统计:")
            print(f"聚类数量: {len(self.clusters)}")
            print(f"平均聚类大小: {avg_cluster_size:.2f}")
            print(f"最大聚类大小: {max_cluster_size}")
            print(f"最小聚类大小: {min_cluster_size}")
            print(f"聚类覆盖率: {sum(cluster_sizes)/n_nodes:.2%}")

    def export_to_csv(self, filename: str = "glc_results.csv"):
        """
        将结果导出为CSV格式

        Args:
            filename: CSV文件名
        """
        if not self.glc_values:
            self.run_glc_algorithm()

        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            writer.writerow(['节点ID', 'GLC值', '度数', 'k-shell值', '局部影响力', '是否全局关键节点'])

            # 写入数据
            sorted_nodes = sorted(self.glc_values.items(), key=lambda x: x[1], reverse=True)
            for node, glc_value in sorted_nodes:
                degree = self.graph.degree(node)
                k_shell = self.k_shell_values.get(node, 0)
                local_inf = self.local_influence.get(node, 0)
                is_global = node in self.global_critical_nodes

                writer.writerow([node, glc_value, degree, k_shell, local_inf, is_global])

        print(f"结果已导出为CSV文件: {filename}")


# 使用示例和测试函数
def main():
    """主函数，演示GLC算法的使用"""

    # 创建GLC算法实例
    glc = GLCCentrality(nx.Graph())

    # 加载网络数据（以karate网络为例）
    print("加载网络数据...")
    graph = glc.load_network_from_file("networks/karate.txt")
    glc.graph = graph

    print(f"加载完成：节点数 = {len(graph.nodes())}, 边数 = {len(graph.edges())}")

    # 运行GLC算法
    glc.run_glc_algorithm()

    # 打印详细结果
    glc.print_results(top_k=10)

    # 分析网络属性
    glc.analyze_network_properties()

    # 与其他中心性指标比较
    glc.compare_with_other_centralities(top_k=5)

    # 保存结果到文件
    glc.save_results_to_file("karate_glc_results.txt")

    # 导出CSV文件
    glc.export_to_csv("karate_glc_results.csv")

    return glc


def test_multiple_networks():
    """测试多个网络数据集"""

    # 可用的网络文件列表
    networks = [
        ("networks/karate.txt", "Karate Club"),
        ("networks/power.txt", "Power Grid"),
        ("networks/ba.txt", "Barabási-Albert"),
        ("networks/er.txt", "Erdős-Rényi"),
        ("networks/ws.txt", "Watts-Strogatz")
    ]

    results_summary = []

    for network_file, network_name in networks:
        try:
            print(f"\n{'='*60}")
            print(f"测试网络: {network_name}")
            print(f"文件: {network_file}")
            print('='*60)

            # 创建GLC实例
            glc = GLCCentrality(nx.Graph())

            # 加载网络
            graph = glc.load_network_from_file(network_file)
            glc.graph = graph

            # 运行算法
            glc.run_glc_algorithm()

            # 获取基本统计信息
            n_nodes = len(graph.nodes())
            n_edges = len(graph.edges())
            n_clusters = len(glc.clusters)
            n_global_nodes = len(glc.global_critical_nodes)

            # 获取前3个重要节点
            top_3 = glc.get_top_k_nodes(3)

            results_summary.append({
                'network': network_name,
                'nodes': n_nodes,
                'edges': n_edges,
                'clusters': n_clusters,
                'global_nodes': n_global_nodes,
                'top_3': top_3
            })

            # 打印简要结果
            print(f"节点数: {n_nodes}, 边数: {n_edges}")
            print(f"聚类数: {n_clusters}, 全局关键节点数: {n_global_nodes}")
            print("前3个重要节点:")
            for i, (node, glc_value) in enumerate(top_3, 1):
                print(f"  {i}. 节点{node}: GLC={glc_value:.4f}")

        except FileNotFoundError:
            print(f"文件 {network_file} 不存在，跳过...")
        except Exception as e:
            print(f"处理 {network_name} 时出错: {e}")

    # 打印总结
    print(f"\n{'='*60}")
    print("所有网络测试总结")
    print('='*60)
    print("网络名称\t\t节点数\t边数\t聚类数\t全局节点数\t最重要节点")
    print("-" * 80)

    for result in results_summary:
        top_node = result['top_3'][0][0] if result['top_3'] else "N/A"
        print(f"{result['network']:<15}\t{result['nodes']}\t{result['edges']}\t{result['clusters']}\t{result['global_nodes']}\t\t{top_node}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行多网络测试
        test_multiple_networks()
    else:
        # 运行单个网络示例
        main()