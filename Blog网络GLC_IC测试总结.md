# Blog网络GLC算法IC模型测试总结

## 测试概述

成功在Blog网络上测试了GLC算法与IC（Independent Cascade）模型的结合，使用k=50个种子节点进行影响力传播评估。

## 网络基本信息

- **网络名称**: Blog Network (blog-int.txt)
- **网络规模**: 3982个节点，6803条边
- **网络密度**: 0.00086 (稀疏网络)
- **种子节点数**: 50个
- **模拟次数**: 500次蒙特卡洛模拟

## GLC算法在Blog网络上的表现

### 1. 算法执行结果

**聚类检测结果**:
- 检测到317个聚类
- 选择了317个全局关键节点
- 聚类覆盖率: 约80% (3186/3982节点)

**GLC前10个重要节点**:
[10, 232, 661, 20, 238, 492, 368, 175, 1592, 126]

### 2. IC模型影响力传播结果

| 传播概率 | GLC影响力 | 覆盖率 | 排名 |
|---------|-----------|--------|------|
| p=0.05  | 109.96节点 | 2.76%  | 4/7  |
| p=0.1   | 187.82节点 | 4.72%  | 6/7  |
| p=0.2   | 449.02节点 | 11.28% | 6/7  |

## 与其他中心性指标的比较

### 完整比较结果表

| 方法名称 | p=0.05 | p=0.1 | p=0.2 | 平均排名 |
|---------|--------|-------|-------|----------|
| **GLC** | **109.96** | **187.82** | **449.02** | **5.3** |
| Degree | 164.41 | 316.93 | 733.67 | 1.7 |
| Betweenness | 142.56 | 283.15 | 695.51 | 3.0 |
| Closeness | 109.01 | 202.82 | 534.68 | 4.7 |
| Eigenvector | 88.59 | 149.70 | 391.42 | 6.7 |
| PageRank | 160.44 | 322.55 | 753.22 | 1.3 |
| K-Shell | 93.60 | 161.71 | 424.87 | 6.0 |

### GLC性能分析

**p=0.05 (低传播概率)**:
- vs Closeness: +0.9% ✅ (GLC更好)
- vs Eigenvector: +24.1% ✅ (GLC更好)  
- vs K-Shell: +17.5% ✅ (GLC更好)
- **胜出率**: 3/6 (50.0%)

**p=0.1 (中等传播概率)**:
- vs Eigenvector: +25.5% ✅ (GLC更好)
- vs K-Shell: +16.1% ✅ (GLC更好)
- **胜出率**: 2/6 (33.3%)

**p=0.2 (高传播概率)**:
- vs Eigenvector: +14.7% ✅ (GLC更好)
- vs K-Shell: +5.7% ✅ (GLC更好)
- **胜出率**: 2/6 (33.3%)

## 关键发现

### 1. GLC算法特点

**优势**:
- 在低传播概率下表现相对较好
- 相比Eigenvector和K-Shell中心性有显著优势
- 聚类检测能力强，识别出317个有意义的聚类

**劣势**:
- 在Blog网络中整体排名中等偏下
- 相比Degree、PageRank等简单指标效果不佳
- 在高传播概率下优势不明显

### 2. Blog网络特性分析

**网络结构特点**:
- 稀疏网络，平均度数约3.4
- 具有明显的聚类结构（317个聚类）
- 度分布不均匀，存在高度节点

**传播特性**:
- 低传播概率下，影响力传播范围有限
- 高传播概率下，度中心性和PageRank表现突出
- 网络的稀疏性限制了信息传播范围

### 3. 算法适用性分析

**GLC算法在Blog网络中的表现说明**:

1. **聚类感知能力**: GLC成功识别了网络中的聚类结构，这是其核心优势
2. **局部-全局平衡**: 在稀疏网络中，纯度中心性可能更有效
3. **网络类型敏感**: GLC在不同类型网络中表现差异较大

## 与其他网络的对比

### 跨网络性能比较

| 网络类型 | 节点数 | GLC胜出率 | 最佳表现概率 |
|---------|--------|-----------|-------------|
| Karate Club | 34 | 16.7% | p=0.05 |
| **Blog Network** | **3982** | **33.3%** | **p=0.05** |
| Power Grid | 4941 | 66.7% | 所有概率 |

**观察结果**:
- GLC在大型稀疏网络(Power Grid)中表现最佳
- 在中等规模网络(Blog)中表现中等
- 在小型密集网络(Karate)中表现较差

## 实际应用价值

### 1. Blog网络影响力分析

**适用场景**:
- 博客影响力评估
- 信息传播路径分析
- 意见领袖识别

**GLC算法价值**:
- 能够识别网络中的聚类结构
- 提供了不同于传统中心性的视角
- 在特定传播概率下有一定优势

### 2. 算法改进建议

**基于Blog网络测试的改进方向**:

1. **参数优化**: 调整λ参数以适应不同网络密度
2. **聚类算法改进**: 优化聚类检测算法以提高精度
3. **权重调整**: 平衡局部和全局影响力的权重
4. **网络预处理**: 考虑网络的预处理以提高算法效果

## 技术实现细节

### 1. 代码修改

**主要修改内容**:
```python
# 修改主函数使用blog网络
graph = evaluator.glc_algorithm.load_network_from_file("networks/blog-int.txt")

# 调整种子节点数
evaluator.k = min(50, len(graph.nodes()) // 20)

# 保存blog网络专用结果
evaluator.save_results_to_file("blog_glc_ic_results.txt")
```

**测试配置**:
- 传播概率: [0.05, 0.1, 0.2]
- 模拟次数: 500次
- 种子节点数: 50个

### 2. 性能表现

**计算效率**:
- GLC算法运行时间: ~13秒
- IC模拟时间: 每个方法约0.1-0.6秒
- 总测试时间: ~3分钟

**内存使用**:
- 网络加载: 正常
- 中心性计算: 稳定
- 模拟过程: 高效

## 结论

### 1. 总体评价

GLC算法在Blog网络上的测试结果表明：
- **算法稳定性**: 能够成功处理中等规模的真实网络
- **聚类能力**: 展现了良好的网络结构识别能力
- **性能表现**: 在特定条件下有一定优势，但整体表现中等

### 2. 适用性评估

**推荐使用场景**:
- 需要理解网络聚类结构的应用
- 低传播概率的影响力分析
- 作为传统中心性指标的补充

**不推荐场景**:
- 追求最大影响力传播的应用
- 高传播概率的场景
- 对计算效率要求极高的场景

### 3. 研究价值

Blog网络测试为GLC算法研究提供了重要参考：
- 验证了算法在真实网络中的可行性
- 揭示了算法的适用范围和局限性
- 为后续算法改进提供了方向

这次测试成功地将GLC算法应用到了Blog网络，为算法的实际应用价值评估提供了重要数据支撑。
