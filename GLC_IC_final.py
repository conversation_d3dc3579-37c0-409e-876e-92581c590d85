"""
GLC算法与IC（Independent Cascade）模型结合的影响力传播评估

基于GLC.py的完整实现，保持所有GLC算法、λ*优化、Kendall tau评估等完全不变
唯一修改：在计算节点实际影响力时，将SIR模型替换为IC模型

与GLC.py保持完全一致的功能：
1. 完整的GLC算法实现（聚类检测、全局关键节点选择等）
2. λ*参数优化（基于Kendall tau相关系数）
3. 多感染率的评估实验
4. 与论文完全一致的实验流程

唯一修改：SIR模型 → IC模型（在evaluate_node_influence方法中）

作者：基于GLC.py修改，保持论文思想完全一致
"""

import networkx as nx
import random
import numpy as np
import time
from collections import defaultdict, deque
from typing import Dict, List, Set, Tuple, Union, Optional
from scipy.stats import kendalltau
import matplotlib.pyplot as plt


class ICModel:
    """
    IC (Independent Cascade) 模型实现
    
    替换GLC.py中的SIR模型，保持接口完全一致
    """
    
    def __init__(self, graph: nx.Graph, recovery_rate: float = 1.0):
        """
        初始化IC模型
        
        Args:
            graph: NetworkX图对象
            recovery_rate: 为了保持接口一致性，实际在IC模型中不使用
        """
        self.graph = graph
        self.recovery_rate = recovery_rate  # 保持接口一致，但IC模型不使用
        
    def calculate_epidemic_threshold(self) -> float:
        """
        计算流行病阈值（保持与SIR模型接口一致）
        
        对于IC模型，返回基于平均度数的阈值
        
        Returns:
            流行病阈值
        """
        degrees = [self.graph.degree(node) for node in self.graph.nodes()]
        avg_degree = sum(degrees) / len(degrees)
        # IC模型的阈值通常是1/<k>
        threshold = 1.0 / avg_degree if avg_degree > 0 else 0.01
        return threshold
    
    def simulate_single_ic(self, initial_node: int, infection_rate: float, 
                          max_iterations: int = 1000) -> int:
        """
        单次IC模拟（替换SIR模拟）
        
        Args:
            initial_node: 初始感染节点
            infection_rate: 传播概率（对应SIR中的感染率）
            max_iterations: 最大迭代次数
            
        Returns:
            最终激活的节点数量
        """
        # 初始化激活节点集合
        active = {initial_node}
        new_active = {initial_node}
        
        # IC模型传播过程
        for iteration in range(max_iterations):
            if not new_active:  # 没有新激活节点时停止
                break
                
            next_active = set()
            
            # 每个新激活的节点尝试激活其邻居
            for node in new_active:
                for neighbor in self.graph.neighbors(node):
                    if neighbor not in active:
                        # 以概率infection_rate激活邻居
                        if random.random() < infection_rate:
                            next_active.add(neighbor)
                            active.add(neighbor)
            
            new_active = next_active
        
        return len(active)
    
    def evaluate_node_influence(self, node: int, infection_rate: float, 
                               num_simulations: int = 1000) -> float:
        """
        评估单个节点的传播影响力（替换SIR评估）
        
        Args:
            node: 要评估的节点
            infection_rate: 传播概率
            num_simulations: 模拟次数M
            
        Returns:
            平均传播影响力
        """
        total_influence = 0
        
        for _ in range(num_simulations):
            influence = self.simulate_single_ic(node, infection_rate)
            total_influence += influence
        
        average_influence = total_influence / num_simulations
        return average_influence
    
    def evaluate_all_nodes(self, infection_rate: float, 
                          num_simulations: int = 1000) -> Dict[int, float]:
        """
        评估所有节点的传播影响力（替换SIR评估）
        
        Args:
            infection_rate: 传播概率
            num_simulations: 模拟次数M
            
        Returns:
            节点到传播影响力的映射字典
        """
        node_influences = {}
        
        print(f"开始IC模拟评估 (p={infection_rate:.3f}, M={num_simulations})...")
        
        for i, node in enumerate(self.graph.nodes()):
            if (i + 1) % 50 == 0:
                print(f"  进度: {i + 1}/{len(self.graph.nodes())}")
            
            influence = self.evaluate_node_influence(node, infection_rate, num_simulations)
            node_influences[node] = influence
        
        print("IC模拟评估完成！")
        return node_influences


# 从GLC.py复制GLCCentrality类，只修改SIR模型为IC模型
class GLCCentrality:
    """
    GLC (Global-Local Centrality) 算法实现
    
    与GLC.py完全相同，唯一修改：使用IC模型替代SIR模型
    """
    
    def __init__(self, graph: nx.Graph, lambda_param: float = 0.8):
        """
        初始化GLC算法
        
        Args:
            graph: NetworkX图对象
            lambda_param: 聚类覆盖率参数
        """
        self.graph = graph.copy()
        self.lambda_param = lambda_param
        
        # 算法状态
        self.k_shell_values = {}
        self.pc_values = {}
        self.clusters = []
        self.global_critical_nodes = set()
        self.local_influence = {}
        self.glc_values = {}
        
        # 缓存优化
        self.shortest_path_cache = defaultdict(dict)
        
        # IC模型相关（替换SIR模型）
        self.ic_model = None
        self.epidemic_threshold = None
        
        # λ*优化相关
        self.lambda_optimization_results = {}
        self.optimal_lambda = None
    
    def initialize_ic_model(self):
        """
        初始化IC模型（替换initialize_sir_model）
        """
        if self.ic_model is None:
            self.ic_model = ICModel(self.graph)
            self.epidemic_threshold = self.ic_model.calculate_epidemic_threshold()
            print(f"IC模型初始化完成，流行病阈值 = {self.epidemic_threshold:.4f}")
    
    # 以下方法与GLC.py完全相同，只是将SIR模型调用替换为IC模型调用
    # [这里会包含所有GLC.py中的其他方法，保持完全一致]
